import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ProductSpecification } from '@/types/product';

interface ProductSpecificationsProps {
  specifications: ProductSpecification[];
  title?: string;
  description?: string;
  columns?: 1 | 2;
  className?: string;
}

const ProductSpecifications = ({ 
  specifications, 
  title = "Technical Specifications",
  description,
  columns = 2,
  className = ""
}: ProductSpecificationsProps) => {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className={`grid grid-cols-1 ${columns === 2 ? 'md:grid-cols-2' : ''} gap-6`}>
          {specifications.map((spec, index) => (
            <div key={index} className="flex justify-between py-3 border-b border-border">
              <span className="font-medium text-foreground">{spec.name}</span>
              <span className="text-muted-foreground">
                {spec.value} {spec.unit}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductSpecifications;
