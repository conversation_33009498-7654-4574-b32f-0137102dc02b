// CMS Data Models for Nile Pro MEP Website

// Base interfaces
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

export interface MediaItem {
  url: string;
  alt: string;
  caption?: string;
  type: 'image' | 'video' | 'document';
  size?: string;
  format?: string;
}

// Service Types
export interface Service extends BaseEntity {
  title: string;
  slug: string;
  shortDescription: string;
  fullDescription: string;
  icon: string;
  features: string[];
  badge?: string;
  complexity: 'Low' | 'Medium' | 'High';
  duration: string;
  rating: number;
  images: MediaItem[];
  category: ServiceCategory;
  tags: string[];
}

export type ServiceCategory = 
  | 'electrical'
  | 'mechanical'
  | 'plumbing'
  | 'hvac'
  | 'automation'
  | 'maintenance';

// Solution Types
export interface Solution extends BaseEntity {
  title: string;
  slug: string;
  shortDescription: string;
  fullDescription: string;
  detailedDescription: string;
  icon: string;
  features: string[];
  projects: string;
  category: SolutionCategory;
  services: SolutionService[];
  caseStudies: CaseStudy[];
  stats: Statistic[];
  images: MediaItem[];
  tags: string[];
}

export interface SolutionService {
  title: string;
  description: string;
  icon: string;
}

export interface CaseStudy {
  title: string;
  location: string;
  description: string;
  results: string[];
  image: string;
  year?: string;
  client?: string;
}

export type SolutionCategory = 
  | 'hospitality'
  | 'healthcare'
  | 'industrial'
  | 'office'
  | 'food-beverage'
  | 'commercial';

// Project/Reference Types
export interface Project extends BaseEntity {
  title: string;
  slug: string;
  location: string;
  category: ProjectCategory;
  year: string;
  description: string;
  detailedDescription: string;
  scope: string;
  value: string;
  duration: string;
  client: string;
  image: string;
  gallery: string[];
  features: string[];
  challenges: string[];
  solutions: string[];
  results: string[];
  testimonial?: Testimonial;
  tags: string[];
  status: 'completed' | 'ongoing' | 'planned';
}

export type ProjectCategory = 
  | 'hospitality'
  | 'healthcare'
  | 'industrial'
  | 'commercial'
  | 'residential'
  | 'infrastructure';

// Team Member Types
export interface TeamMember extends BaseEntity {
  name: string;
  position: string;
  department: string;
  email: string;
  phone: string;
  image: string;
  bio: string;
  linkedin?: string;
  specialties: string[];
  experience: string;
  education?: string[];
  certifications?: string[];
  languages?: string[];
}

// Testimonial Types
export interface Testimonial extends BaseEntity {
  name: string;
  position: string;
  company: string;
  quote: string;
  rating: number;
  image: string;
  projectValue?: string;
  completionYear: string;
  projectId?: string;
  category: TestimonialCategory;
}

export type TestimonialCategory = 
  | 'client'
  | 'partner'
  | 'employee'
  | 'vendor';

// Company Information Types
export interface CompanyInfo extends BaseEntity {
  name: string;
  tagline: string;
  description: string;
  foundedYear: number;
  experience: number;
  mission: string;
  vision: string;
  values: CompanyValue[];
  achievements: Achievement[];
  certifications: Certification[];
  stats: Statistic[];
  history: HistoryItem[];
  leadership: TeamMember[];
}

export interface CompanyValue {
  title: string;
  description: string;
  icon: string;
}

export interface Achievement {
  title: string;
  description: string;
  year: string;
  icon?: string;
}

export interface Certification {
  name: string;
  issuer: string;
  year: string;
  expiryYear?: string;
  image?: string;
  description?: string;
}

export interface HistoryItem {
  year: string;
  title: string;
  description: string;
  milestone: boolean;
}

export interface Statistic {
  number: string;
  label: string;
  description?: string;
  icon?: string;
}

// Site Settings Types
export interface SiteSettings extends BaseEntity {
  siteName: string;
  siteDescription: string;
  siteKeywords: string[];
  logo: string;
  favicon: string;
  contactInfo: ContactInfo;
  socialMedia: SocialMediaLinks;
  businessHours: BusinessHours[];
  seoSettings: SEOSettings;
  maintenanceMode: boolean;
  analytics: AnalyticsSettings;
}

export interface ContactInfo {
  phones: string[];
  emails: string[];
  address: Address;
  whatsapp?: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface SocialMediaLinks {
  facebook?: string;
  twitter?: string;
  linkedin?: string;
  instagram?: string;
  youtube?: string;
}

export interface BusinessHours {
  day: string;
  open: string;
  close: string;
  closed: boolean;
}

export interface SEOSettings {
  defaultTitle: string;
  defaultDescription: string;
  defaultKeywords: string[];
  ogImage: string;
  twitterHandle?: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
}

export interface AnalyticsSettings {
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  enabled: boolean;
}

// CMS User Types
export interface CMSUser extends BaseEntity {
  username: string;
  email: string;
  role: UserRole;
  permissions: Permission[];
  lastLogin?: string;
  profile: UserProfile;
}

export type UserRole = 'admin' | 'editor' | 'viewer';

export interface Permission {
  resource: string;
  actions: ('create' | 'read' | 'update' | 'delete')[];
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  avatar?: string;
  phone?: string;
  department?: string;
}

// CMS Content Types Union
export type CMSContent = 
  | Service
  | Solution
  | Project
  | TeamMember
  | Testimonial
  | CompanyInfo
  | SiteSettings;

// CMS API Response Types
export interface CMSResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface CMSListResponse<T> extends CMSResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
