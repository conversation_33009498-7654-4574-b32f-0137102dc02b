import { useState } from 'react';
import { Menu, X, ChevronDown, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { getAllProducts } from '@/utils/productUtils';


const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [activeMobileSubmenu, setActiveMobileSubmenu] = useState(null);

  const products = getAllProducts();

  const navItems = [
    { name: 'Home', href: '/' },
    { name: 'Corporate', href: '/corporate' },
    {
      name: 'Products',
      href: '/products',
      submenu: products.map(product => ({
        name: product.title,
        href: `/products/${product.slug}`
      }))
    },
    {
      name: 'Solutions',
      href: '/solutions',
      submenu: [
        { name: 'Hospitality', href: '/solutions/hospitality' },
        { name: 'Healthcare', href: '/solutions/healthcare' },
        { name: 'Pharmaceutical', href: '/solutions/pharmaceutical' },
        { name: 'Business', href: '/solutions/business' },
        { name: 'Food and Beverage', href: '/solutions/food-beverage' },
        { name: 'Commercial', href: '/solutions/commercial' }
      ]
    },
    { name: 'References', href: '/references' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-18">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <img
                src="/logo.png"
                alt="Nile Pro Logo"
                className="h-12 w-auto transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-primary opacity-0 group-hover:opacity-10 rounded-lg transition-opacity duration-300"></div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => (
              <div
                key={item.name}
                className="relative group"
                onMouseEnter={() => item.submenu && setActiveDropdown(item.name)}
                onMouseLeave={() => item.submenu && setActiveDropdown(null)}
              >
                {item.submenu ? (
                  <div className="relative">
                    <button
                      className="flex items-center text-foreground hover:text-primary transition-all duration-300 font-medium py-2 px-3 rounded-lg hover:bg-primary/5 group"
                    >
                      {item.name}
                      <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-300 ${activeDropdown === item.name ? 'rotate-180' : ''}`} />
                    </button>
                    {activeDropdown === item.name && (
                      <div className="absolute top-full left-0 pt-2 w-64 z-50 animate-in slide-in-from-top-2 duration-200">
                        <div className="bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-xl max-h-96 overflow-y-auto">
                          {item.name === 'Products' && (
                            <div className="px-4 py-3 border-b border-border/50">
                              <Link
                                to="/products"
                                className="text-sm font-semibold text-primary hover:text-primary-dark transition-colors flex items-center group"
                                onClick={() => setActiveDropdown(null)}
                              >
                                View All Products
                                <ArrowRight className="ml-2 h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                              </Link>
                            </div>
                          )}
                          {item.submenu.map((subItem) => (
                            <Link
                              key={typeof subItem === 'string' ? subItem : subItem.name}
                              to={typeof subItem === 'string' ? item.href : subItem.href}
                              className="block px-4 py-3 text-sm text-muted-foreground hover:text-primary hover:bg-primary/5 transition-all duration-200 rounded-lg mx-2 my-1"
                              onClick={() => setActiveDropdown(null)}
                            >
                              {typeof subItem === 'string' ? subItem : subItem.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    to={item.href}
                    className="text-foreground hover:text-primary transition-all duration-300 font-medium py-2 px-3 rounded-lg hover:bg-primary/5 relative group"
                  >
                    {item.name}
                    <span className="absolute bottom-0 left-3 right-3 h-0.5 bg-primary scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                  </Link>
                )}
              </div>
            ))}
            <Button className="bg-gradient-primary hover:opacity-90 shadow-primary hover:shadow-lg transition-all duration-300 hover:scale-105">
              Get Quote
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-3 min-h-[44px] min-w-[44px]"
              aria-label="Toggle navigation menu"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border/50 bg-background/95 backdrop-blur-md animate-in slide-in-from-top-2 duration-300">
            <div className="flex flex-col space-y-2">
              {navItems.map((item) => (
                <div key={item.name}>
                  {item.submenu ? (
                    <div>
                      <button
                        className="w-full flex items-center justify-between text-foreground hover:text-primary transition-all duration-300 font-medium px-4 py-3 min-h-[44px] rounded-lg hover:bg-primary/5"
                        onClick={() => setActiveMobileSubmenu(activeMobileSubmenu === item.name ? null : item.name)}
                        aria-expanded={activeMobileSubmenu === item.name}
                      >
                        {item.name}
                        <ChevronDown className={`h-4 w-4 transition-transform duration-300 ${activeMobileSubmenu === item.name ? 'rotate-180' : ''}`} />
                      </button>
                      {activeMobileSubmenu === item.name && (
                        <div className="bg-muted/30 py-2 mx-2 rounded-lg animate-in slide-in-from-top-1 duration-200">
                          {item.name === 'Products' && (
                            <Link
                              to="/products"
                              className="block px-6 py-3 text-sm font-semibold text-primary hover:text-primary-dark transition-colors min-h-[44px] flex items-center rounded-lg hover:bg-primary/10 mx-2"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              View All Products
                              <ArrowRight className="ml-2 h-3 w-3" />
                            </Link>
                          )}
                          {item.submenu.map((subItem) => (
                            <Link
                              key={typeof subItem === 'string' ? subItem : subItem.name}
                              to={typeof subItem === 'string' ? item.href : subItem.href}
                              className="block px-6 py-3 text-sm text-muted-foreground hover:text-primary transition-colors min-h-[44px] flex items-center rounded-lg hover:bg-primary/5 mx-2"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              {typeof subItem === 'string' ? subItem : subItem.name}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.href}
                      className="block text-foreground hover:text-primary transition-all duration-300 font-medium px-4 py-3 min-h-[44px] flex items-center rounded-lg hover:bg-primary/5"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
              <div className="px-4 pt-2">
                <Button className="w-full bg-gradient-primary hover:opacity-90 shadow-primary min-h-[44px]">
                  Get Quote
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;