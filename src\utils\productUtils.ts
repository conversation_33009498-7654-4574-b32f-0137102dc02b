import { Product, ProductCategory } from '@/types/product';
import { products, productCategories } from '@/data/products';

/**
 * Get all products
 */
export const getAllProducts = (): Product[] => {
  return products.filter(product => product.isActive);
};

/**
 * Get product by ID
 */
export const getProductById = (id: string): Product | undefined => {
  return products.find(product => product.id === id && product.isActive);
};

/**
 * Get product by slug
 */
export const getProductBySlug = (slug: string): Product | undefined => {
  return products.find(product => product.slug === slug && product.isActive);
};

/**
 * Get products by category
 */
export const getProductsByCategory = (category: ProductCategory): Product[] => {
  return products.filter(product => product.category === category && product.isActive);
};

/**
 * Get products by tag
 */
export const getProductsByTag = (tag: string): Product[] => {
  return products.filter(product => 
    product.tags.some(productTag => 
      productTag.toLowerCase().includes(tag.toLowerCase())
    ) && product.isActive
  );
};

/**
 * Search products by title or description
 */
export const searchProducts = (query: string): Product[] => {
  const lowercaseQuery = query.toLowerCase();
  return products.filter(product => 
    (product.title.toLowerCase().includes(lowercaseQuery) ||
     product.shortDescription.toLowerCase().includes(lowercaseQuery) ||
     product.fullDescription.toLowerCase().includes(lowercaseQuery) ||
     product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))) &&
    product.isActive
  );
};

/**
 * Get all product categories
 */
export const getAllCategories = () => {
  return productCategories;
};

/**
 * Get category info by ID
 */
export const getCategoryById = (id: ProductCategory) => {
  return productCategories.find(category => category.id === id);
};

/**
 * Generate product URL slug
 */
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

/**
 * Get related products (same category, excluding current product)
 */
export const getRelatedProducts = (currentProduct: Product, limit: number = 3): Product[] => {
  return products
    .filter(product => 
      product.category === currentProduct.category && 
      product.id !== currentProduct.id && 
      product.isActive
    )
    .slice(0, limit);
};

/**
 * Get featured products (can be customized based on business logic)
 */
export const getFeaturedProducts = (limit: number = 6): Product[] => {
  return products
    .filter(product => product.isActive)
    .slice(0, limit);
};
