#!/usr/bin/env node

/**
 * Data Migration Script: JSON to Strapi
 * 
 * This script migrates existing JSON data to Strapi CMS
 * Run with: node scripts/migrate-to-strapi.js
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1337';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;

if (!STRAPI_API_TOKEN) {
  console.error('❌ STRAPI_API_TOKEN environment variable is required');
  process.exit(1);
}

// Create Strapi API client
const strapiApi = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${STRAPI_API_TOKEN}`
  }
});

// Helper function to read JSON data files
const readJsonData = (filePath) => {
  try {
    const fullPath = path.join(__dirname, '..', filePath);
    const data = fs.readFileSync(fullPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ Error reading ${filePath}:`, error.message);
    return null;
  }
};

// Helper function to create Strapi entry
const createStrapiEntry = async (endpoint, data) => {
  try {
    const response = await strapiApi.post(endpoint, { data });
    console.log(`✅ Created ${endpoint}: ${data.title || data.name || data.id}`);
    return response.data;
  } catch (error) {
    console.error(`❌ Error creating ${endpoint}:`, error.response?.data || error.message);
    return null;
  }
};

// Migration functions
const migrateServices = async () => {
  console.log('\n📦 Migrating Services...');
  
  // Import services data (you'll need to adjust the import path)
  const servicesModule = await import('../src/data/cms/services.js');
  const services = servicesModule.services;

  for (const service of services) {
    const strapiData = {
      title: service.title,
      slug: service.slug,
      shortDescription: service.shortDescription,
      fullDescription: service.fullDescription,
      icon: service.icon,
      features: service.features,
      badge: service.badge,
      complexity: service.complexity,
      duration: service.duration,
      rating: service.rating,
      category: service.category,
      tags: service.tags,
      publishedAt: service.isActive ? new Date().toISOString() : null
    };

    await createStrapiEntry('services', strapiData);
  }
};

const migrateSolutions = async () => {
  console.log('\n🏢 Migrating Solutions...');
  
  const solutionsModule = await import('../src/data/cms/solutions.js');
  const solutions = solutionsModule.solutions;

  for (const solution of solutions) {
    const strapiData = {
      title: solution.title,
      slug: solution.slug,
      shortDescription: solution.shortDescription,
      fullDescription: solution.fullDescription,
      detailedDescription: solution.detailedDescription,
      icon: solution.icon,
      features: solution.features,
      projects: solution.projects,
      category: solution.category,
      services: solution.services,
      caseStudies: solution.caseStudies,
      stats: solution.stats,
      tags: solution.tags,
      publishedAt: solution.isActive ? new Date().toISOString() : null
    };

    await createStrapiEntry('solutions', strapiData);
  }
};

const migrateProjects = async () => {
  console.log('\n🏗️ Migrating Projects...');
  
  const projectsModule = await import('../src/data/cms/projects.js');
  const projects = projectsModule.projects;

  for (const project of projects) {
    const strapiData = {
      title: project.title,
      slug: project.slug,
      location: project.location,
      category: project.category,
      year: project.year,
      description: project.description,
      detailedDescription: project.detailedDescription,
      scope: project.scope,
      value: project.value,
      duration: project.duration,
      client: project.client,
      features: project.features,
      challenges: project.challenges,
      solutions: project.solutions,
      results: project.results,
      tags: project.tags,
      status: project.status,
      publishedAt: project.isActive ? new Date().toISOString() : null
    };

    await createStrapiEntry('projects', strapiData);
  }
};

const migrateTeamMembers = async () => {
  console.log('\n👥 Migrating Team Members...');
  
  const teamModule = await import('../src/data/cms/team.js');
  const teamMembers = teamModule.teamMembers;

  for (const member of teamMembers) {
    const strapiData = {
      name: member.name,
      position: member.position,
      department: member.department,
      email: member.email,
      phone: member.phone,
      bio: member.bio,
      linkedin: member.linkedin,
      specialties: member.specialties,
      experience: member.experience,
      education: member.education,
      certifications: member.certifications,
      languages: member.languages,
      publishedAt: member.isActive ? new Date().toISOString() : null
    };

    await createStrapiEntry('team-members', strapiData);
  }
};

const migrateTestimonials = async () => {
  console.log('\n💬 Migrating Testimonials...');
  
  const projectsModule = await import('../src/data/cms/projects.js');
  const testimonials = projectsModule.testimonials;

  for (const testimonial of testimonials) {
    const strapiData = {
      name: testimonial.name,
      position: testimonial.position,
      company: testimonial.company,
      quote: testimonial.quote,
      rating: testimonial.rating,
      projectValue: testimonial.projectValue,
      completionYear: testimonial.completionYear,
      category: testimonial.category,
      publishedAt: testimonial.isActive ? new Date().toISOString() : null
    };

    await createStrapiEntry('testimonials', strapiData);
  }
};

const migrateCompanyInfo = async () => {
  console.log('\n🏢 Migrating Company Info...');
  
  const companyModule = await import('../src/data/cms/company.js');
  const companyInfo = companyModule.companyInfo;

  const strapiData = {
    name: companyInfo.name,
    tagline: companyInfo.tagline,
    description: companyInfo.description,
    foundedYear: companyInfo.foundedYear,
    experience: companyInfo.experience,
    mission: companyInfo.mission,
    vision: companyInfo.vision,
    values: companyInfo.values,
    achievements: companyInfo.achievements,
    certifications: companyInfo.certifications,
    stats: companyInfo.stats,
    history: companyInfo.history,
    publishedAt: companyInfo.isActive ? new Date().toISOString() : null
  };

  await createStrapiEntry('company-info', strapiData);
};

const migrateSiteSettings = async () => {
  console.log('\n⚙️ Migrating Site Settings...');
  
  const settingsModule = await import('../src/data/cms/settings.js');
  const siteSettings = settingsModule.siteSettings;

  const strapiData = {
    siteName: siteSettings.siteName,
    siteDescription: siteSettings.siteDescription,
    siteKeywords: siteSettings.siteKeywords,
    contactInfo: siteSettings.contactInfo,
    socialMedia: siteSettings.socialMedia,
    businessHours: siteSettings.businessHours,
    seoSettings: siteSettings.seoSettings,
    maintenanceMode: siteSettings.maintenanceMode,
    analytics: siteSettings.analytics,
    publishedAt: siteSettings.isActive ? new Date().toISOString() : null
  };

  await createStrapiEntry('site-settings', strapiData);
};

// Main migration function
const runMigration = async () => {
  console.log('🚀 Starting data migration to Strapi...');
  console.log(`📡 Strapi URL: ${STRAPI_URL}`);

  try {
    // Test Strapi connection
    await strapiApi.get('/');
    console.log('✅ Connected to Strapi successfully');

    // Run migrations
    await migrateServices();
    await migrateSolutions();
    await migrateProjects();
    await migrateTeamMembers();
    await migrateTestimonials();
    await migrateCompanyInfo();
    await migrateSiteSettings();

    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Check your Strapi admin panel to verify the data');
    console.log('2. Update your frontend environment variables:');
    console.log('   - REACT_APP_USE_STRAPI=true');
    console.log('   - REACT_APP_STRAPI_URL=http://localhost:1337');
    console.log('   - REACT_APP_STRAPI_API_TOKEN=your-token');
    console.log('3. Test your frontend with Strapi data');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
};

// Run migration if called directly
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration,
  migrateServices,
  migrateSolutions,
  migrateProjects,
  migrateTeamMembers,
  migrateTestimonials,
  migrateCompanyInfo,
  migrateSiteSettings
};
