import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Users,
  Target,
  Heart,
  Shield,
  Lightbulb,
  Globe
} from 'lucide-react';

const Corporate = () => {
  const stats = [
    { number: "9+", label: "Years Experience", icon: <Globe className="h-6 w-6" /> },
    { number: "500+", label: "Projects Completed", icon: <Building2 className="h-6 w-6" /> },
    { number: "50+", label: "Expert Engineers", icon: <Users className="h-6 w-6" /> },
    { number: "100%", label: "Client Satisfaction", icon: <Heart className="h-6 w-6" /> }
  ];

  const values = [
    {
      icon: <Target className="h-8 w-8" />,
      title: "Excellence",
      description: "We strive for excellence in every project, delivering superior quality that exceeds expectations and sets industry standards."
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Integrity",
      description: "We conduct business with the highest ethical standards, building trust through transparency and honest communication."
    },
    {
      icon: <Lightbulb className="h-8 w-8" />,
      title: "Innovation",
      description: "We embrace cutting-edge technologies and innovative solutions to deliver efficient and sustainable MEP systems."
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: "Customer Focus",
      description: "Our clients are at the center of everything we do. We listen, understand, and deliver solutions that meet their unique needs."
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Teamwork",
      description: "We believe in the power of collaboration, working together to achieve common goals and deliver exceptional results."
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: "Sustainability",
      description: "We are committed to environmental responsibility, implementing green technologies and sustainable practices."
    }
  ];





  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Corporate Overview
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Leading MEP construction company with years of expertise in mechanical, electrical, and plumbing installations across diverse industries
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-primary">About Nile Pro MEP</h2>
              <p className="text-lg text-muted-foreground">
                Nile Pro for Construction specializes in MEP Installation Works, providing comprehensive mechanical, electrical, and plumbing solutions for commercial and industrial projects across the Middle East and Africa.
              </p>
              <p className="text-lg text-muted-foreground">
                With our experienced team of engineers and cutting-edge technology, we deliver high-quality installations that meet international standards and exceed client expectations. Our commitment to excellence has made us a trusted partner for major construction projects.
              </p>
              <Button size="lg" className="bg-gradient-primary hover:opacity-90 shadow-primary">
                Learn More About Our Services
              </Button>
            </div>
            <div className="aspect-video rounded-lg overflow-hidden shadow-elegant">
              <img
                src="https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=600&h=400&fit=crop"
                alt="Modern MEP construction project"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="text-white">
                      {stat.icon}
                    </div>
                  </div>
                  <h3 className="text-3xl font-bold text-primary mb-2">{stat.number}</h3>
                  <p className="text-muted-foreground font-medium">{stat.label}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>



      {/* Company Values */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Our <span className="text-primary">Values</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              The principles that guide our work and define our commitment to excellence
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                    <div className="text-white">
                      {value.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-4">{value.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>







      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
            Ready to Work with <span className="text-primary">Industry Leaders?</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Partner with Nile Pro MEP for your next project and experience the difference that expertise,
            innovation, and commitment to excellence can make.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-gradient-primary hover:opacity-90 shadow-primary">
              Start Your Project
            </Button>
            <Button size="lg" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
              Download Company Profile
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Corporate;