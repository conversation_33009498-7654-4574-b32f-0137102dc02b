import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Users,
  Award,
  Target,
  Heart,
  Shield,
  Lightbulb,
  Globe,
  CheckCircle,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Linkedin
} from 'lucide-react';

const Corporate = () => {
  const stats = [
    { number: "15+", label: "Years Experience", icon: <Calendar className="h-6 w-6" /> },
    { number: "500+", label: "Projects Completed", icon: <Building2 className="h-6 w-6" /> },
    { number: "50+", label: "Expert Engineers", icon: <Users className="h-6 w-6" /> },
    { number: "25+", label: "Industry Awards", icon: <Award className="h-6 w-6" /> }
  ];

  const values = [
    {
      icon: <Target className="h-8 w-8" />,
      title: "Excellence",
      description: "We strive for excellence in every project, delivering superior quality that exceeds expectations and sets industry standards."
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Integrity",
      description: "We conduct business with the highest ethical standards, building trust through transparency and honest communication."
    },
    {
      icon: <Lightbulb className="h-8 w-8" />,
      title: "Innovation",
      description: "We embrace cutting-edge technologies and innovative solutions to deliver efficient and sustainable MEP systems."
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: "Customer Focus",
      description: "Our clients are at the center of everything we do. We listen, understand, and deliver solutions that meet their unique needs."
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Teamwork",
      description: "We believe in the power of collaboration, working together to achieve common goals and deliver exceptional results."
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: "Sustainability",
      description: "We are committed to environmental responsibility, implementing green technologies and sustainable practices."
    }
  ];

  const leadership = [
    {
      name: "Ahmed Hassan",
      position: "Chief Executive Officer",
      experience: "20+ years in MEP construction",
      image: "/placeholder.svg",
      bio: "Visionary leader with extensive experience in large-scale MEP projects across the Middle East and Africa.",
      linkedin: "#"
    },
    {
      name: "Sarah Mohamed",
      position: "Chief Technical Officer",
      experience: "18+ years in engineering",
      image: "/placeholder.svg",
      bio: "Expert engineer specializing in HVAC systems and sustainable building technologies.",
      linkedin: "#"
    },
    {
      name: "Omar Farouk",
      position: "Operations Director",
      experience: "15+ years in project management",
      image: "/placeholder.svg",
      bio: "Operations expert ensuring seamless project delivery and quality control across all installations.",
      linkedin: "#"
    },
    {
      name: "Fatima Ali",
      position: "Business Development Director",
      experience: "12+ years in business development",
      image: "/placeholder.svg",
      bio: "Strategic leader driving business growth and fostering long-term client relationships.",
      linkedin: "#"
    }
  ];

  const certifications = [
    { name: "ISO 9001:2015", description: "Quality Management System", year: "2023" },
    { name: "ISO 14001:2015", description: "Environmental Management", year: "2023" },
    { name: "ISO 45001:2018", description: "Occupational Health & Safety", year: "2023" },
    { name: "LEED Certified", description: "Green Building Certification", year: "2022" },
    { name: "ASHRAE Member", description: "HVAC Industry Standards", year: "2021" },
    { name: "NFPA Compliance", description: "Fire Protection Standards", year: "2023" }
  ];

  const awards = [
    { title: "Best MEP Contractor 2023", organization: "Construction Excellence Awards", year: "2023" },
    { title: "Innovation in HVAC Design", organization: "Engineering Society", year: "2022" },
    { title: "Sustainable Building Award", organization: "Green Building Council", year: "2022" },
    { title: "Safety Excellence Award", organization: "Construction Safety Board", year: "2021" },
    { title: "Client Satisfaction Award", organization: "Industry Association", year: "2021" }
  ];

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Corporate Overview
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Leading MEP construction company with years of expertise in mechanical, electrical, and plumbing installations across diverse industries
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-primary">About Nile Pro MEP</h2>
              <p className="text-lg text-muted-foreground">
                Nile Pro for Construction specializes in MEP Installation Works, providing comprehensive mechanical, electrical, and plumbing solutions for commercial and industrial projects across the Middle East and Africa.
              </p>
              <p className="text-lg text-muted-foreground">
                With our experienced team of engineers and cutting-edge technology, we deliver high-quality installations that meet international standards and exceed client expectations. Our commitment to excellence has made us a trusted partner for major construction projects.
              </p>
              <Button size="lg" className="bg-gradient-primary hover:opacity-90 shadow-primary">
                Learn More About Our Services
              </Button>
            </div>
            <div className="aspect-video rounded-lg overflow-hidden shadow-elegant">
              <img
                src="https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=600&h=400&fit=crop"
                alt="Modern MEP construction project"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="text-white">
                      {stat.icon}
                    </div>
                  </div>
                  <h3 className="text-3xl font-bold text-primary mb-2">{stat.number}</h3>
                  <p className="text-muted-foreground font-medium">{stat.label}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Company History */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Our <span className="text-primary">Journey</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From humble beginnings to industry leadership, discover the milestones that shaped Nile Pro MEP
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary/20"></div>

            <div className="space-y-12">
              {[
                { year: "2008", title: "Company Founded", description: "Nile Pro MEP was established with a vision to revolutionize MEP construction in the region." },
                { year: "2012", title: "First Major Project", description: "Completed our first large-scale hotel MEP installation, setting new quality standards." },
                { year: "2015", title: "ISO Certification", description: "Achieved ISO 9001 certification, demonstrating our commitment to quality management." },
                { year: "2018", title: "Regional Expansion", description: "Expanded operations across the Middle East and North Africa region." },
                { year: "2020", title: "Digital Transformation", description: "Implemented cutting-edge BIM and digital project management systems." },
                { year: "2023", title: "Sustainability Leadership", description: "Became a certified LEED partner, leading sustainable MEP solutions." }
              ].map((milestone, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card className="border-border hover:shadow-primary transition-all duration-300">
                      <CardContent className="p-6">
                        <Badge className="mb-3 bg-primary text-white">{milestone.year}</Badge>
                        <h3 className="text-xl font-bold text-foreground mb-2">{milestone.title}</h3>
                        <p className="text-muted-foreground">{milestone.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="relative z-10">
                    <div className="w-4 h-4 bg-primary rounded-full border-4 border-background"></div>
                  </div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Our <span className="text-primary">Values</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              The principles that guide our work and define our commitment to excellence
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                    <div className="text-white">
                      {value.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-4">{value.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Leadership <span className="text-primary">Team</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Meet the experienced professionals leading Nile Pro MEP to new heights of excellence
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {leadership.map((leader, index) => (
              <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                <CardContent className="p-6">
                  <div className="w-24 h-24 rounded-full overflow-hidden mx-auto mb-4 bg-muted">
                    <img
                      src={leader.image}
                      alt={leader.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="text-lg font-bold text-foreground mb-1">{leader.name}</h3>
                  <p className="text-primary font-medium mb-2">{leader.position}</p>
                  <p className="text-sm text-muted-foreground mb-3">{leader.experience}</p>
                  <p className="text-sm text-muted-foreground mb-4">{leader.bio}</p>
                  <Button variant="outline" size="sm" className="w-full">
                    <Linkedin className="h-4 w-4 mr-2" />
                    Connect
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Certifications & <span className="text-primary">Standards</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Our commitment to quality is validated by internationally recognized certifications and standards
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {certifications.map((cert, index) => (
              <Card key={index} className="border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Badge variant="secondary">{cert.year}</Badge>
                    <CheckCircle className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-bold text-foreground mb-2">{cert.name}</h3>
                  <p className="text-muted-foreground">{cert.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Awards & Recognition */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Awards & <span className="text-primary">Recognition</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Industry recognition for our outstanding contributions to MEP construction excellence
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {awards.map((award, index) => (
              <Card key={index} className="border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Award className="h-8 w-8 text-primary" />
                    <Badge className="bg-primary text-white">{award.year}</Badge>
                  </div>
                  <h3 className="text-lg font-bold text-foreground mb-2">{award.title}</h3>
                  <p className="text-muted-foreground text-sm">{award.organization}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
            Ready to Work with <span className="text-primary">Industry Leaders?</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Partner with Nile Pro MEP for your next project and experience the difference that expertise,
            innovation, and commitment to excellence can make.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-gradient-primary hover:opacity-90 shadow-primary">
              Start Your Project
            </Button>
            <Button size="lg" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
              Download Company Profile
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Corporate;