import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Send,
  MessageSquare,
  Calendar,
  FileText,
  User,
  Building,
  Globe,
  ExternalLink
} from 'lucide-react';
import { useState } from 'react';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    projectType: '',
    budget: '',
    timeline: '',
    message: ''
  });

  const contactInfo = [
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Phone",
      details: ["+20 (2) 123-4567", "+20 (2) 987-6543"],
      subtitle: "Call us anytime",
      action: "tel:+20212345678"
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: "Email",
      details: ["<EMAIL>", "<EMAIL>"],
      subtitle: "Send us your inquiry",
      action: "mailto:<EMAIL>"
    },
    {
      icon: <MapPin className="h-6 w-6" />,
      title: "Location",
      details: ["123 Construction Ave", "Cairo, Egypt 12345"],
      subtitle: "Visit our office",
      action: "https://maps.google.com"
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Working Hours",
      details: ["Mon - Fri: 8:00 AM - 6:00 PM", "Sat: 9:00 AM - 4:00 PM"],
      subtitle: "We're here to help",
      action: null
    }
  ];





  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // You would typically send this data to your backend
  };

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Contact Us
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Ready to discuss your MEP project? Contact our experts for a free consultation
              and discover how we can bring your vision to life.
            </p>


          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
            <Card className="text-center border-border hover:shadow-primary transition-all duration-300">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-primary mb-1">24/7</div>
                <div className="text-sm text-muted-foreground">Support Available</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border hover:shadow-primary transition-all duration-300">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-primary mb-1">2 hrs</div>
                <div className="text-sm text-muted-foreground">Response Time</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border hover:shadow-primary transition-all duration-300">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-primary mb-1">15+</div>
                <div className="text-sm text-muted-foreground">Expert Engineers</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border hover:shadow-primary transition-all duration-300">
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-primary mb-1">200+</div>
                <div className="text-sm text-muted-foreground">Projects Completed</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Enhanced Contact Form */}
            <div className="lg:col-span-2">
              <Card className="border-border shadow-large">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-foreground flex items-center">
                    <Send className="h-6 w-6 text-primary mr-3" />
                    Send Us a Message
                  </CardTitle>
                  <p className="text-muted-foreground">
                    Fill out the form below and we'll get back to you within 2 hours during business hours.
                  </p>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          First Name *
                        </label>
                        <Input
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          placeholder="John"
                          className="border-border min-h-[44px]"
                          required
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Last Name *
                        </label>
                        <Input
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          placeholder="Doe"
                          className="border-border min-h-[44px]"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Email Address *
                        </label>
                        <Input
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder="<EMAIL>"
                          className="border-border min-h-[44px]"
                          required
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Phone Number
                        </label>
                        <Input
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="+20 (2) 123-4567"
                          className="border-border min-h-[44px]"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-foreground mb-2 block">
                        Company
                      </label>
                      <Input
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        placeholder="Your Company Name"
                        className="border-border min-h-[44px]"
                      />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Project Type
                        </label>
                        <select
                          name="projectType"
                          value={formData.projectType}
                          onChange={handleInputChange}
                          className="w-full p-3 border border-border rounded-md bg-background text-foreground min-h-[44px]"
                        >
                          <option value="">Select Project Type</option>
                          <option value="hospitality">Hospitality</option>
                          <option value="healthcare">Healthcare</option>
                          <option value="pharmaceutical">Pharmaceutical</option>
                          <option value="business">Business</option>
                          <option value="food-beverage">Food & Beverage</option>
                          <option value="commercial">Commercial</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Project Budget
                        </label>
                        <select
                          name="budget"
                          value={formData.budget}
                          onChange={handleInputChange}
                          className="w-full p-3 border border-border rounded-md bg-background text-foreground min-h-[44px]"
                        >
                          <option value="">Select Budget Range</option>
                          <option value="under-100k">Under $100K</option>
                          <option value="100k-500k">$100K - $500K</option>
                          <option value="500k-1m">$500K - $1M</option>
                          <option value="1m-5m">$1M - $5M</option>
                          <option value="over-5m">Over $5M</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-foreground mb-2 block">
                        Project Timeline
                      </label>
                      <select
                        name="timeline"
                        value={formData.timeline}
                        onChange={handleInputChange}
                        className="w-full p-3 border border-border rounded-md bg-background text-foreground min-h-[44px]"
                      >
                        <option value="">Select Timeline</option>
                        <option value="immediate">Immediate (Within 1 month)</option>
                        <option value="short">Short-term (1-3 months)</option>
                        <option value="medium">Medium-term (3-6 months)</option>
                        <option value="long">Long-term (6+ months)</option>
                        <option value="planning">Planning Phase</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-foreground mb-2 block">
                        Message *
                      </label>
                      <textarea
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder="Tell us about your project requirements, specific challenges, or any questions you have..."
                        className="w-full p-3 border border-border rounded-md bg-background text-foreground min-h-[120px] resize-vertical"
                        required
                      />
                    </div>

                    <div className="flex gap-4">
                      <Button type="submit" size="lg" className="flex-1 bg-gradient-primary hover:opacity-90 shadow-primary min-h-[44px]">
                        Send Message
                        <Send className="ml-2 h-5 w-5" />
                      </Button>
                      <Button type="button" variant="outline" size="lg" className="min-h-[44px]">
                        <Calendar className="mr-2 h-5 w-5" />
                        Schedule Call
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Enhanced Contact Information */}
            <div className="space-y-6">
              {contactInfo.map((info, index) => (
                <Card key={index} className="border-border hover:shadow-primary transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-primary/10 rounded-lg">
                        <div className="text-primary">
                          {info.icon}
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-foreground mb-1">{info.title}</h3>
                        <p className="text-sm text-muted-foreground mb-2">{info.subtitle}</p>
                        {info.details.map((detail, detailIndex) => (
                          <p key={detailIndex} className="text-sm text-foreground">{detail}</p>
                        ))}
                        {info.action && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-3"
                            onClick={() => window.open(info.action, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            {info.title === 'Phone' ? 'Call Now' :
                              info.title === 'Email' ? 'Send Email' :
                                info.title === 'Location' ? 'Get Directions' : 'Contact'}
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Contact Information */}
          <div className="mt-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
                Get In <span className="text-primary">Touch</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Contact us through any of the following methods. We're here to help with your MEP project needs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Location */}
              <Card className="border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MapPin className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2">Our Location</h3>
                  <p className="text-muted-foreground">
                    El-Nozha District<br />
                    Cairo, Egypt<br />
                    Cairo Governorate 11371
                  </p>
                </CardContent>
              </Card>

              {/* Email */}
              <Card className="border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Mail className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2">Email Us</h3>
                  <p className="text-muted-foreground">
                    <EMAIL><br />
                    <EMAIL>
                  </p>
                  <Button variant="outline" className="mt-4">
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </Button>
                </CardContent>
              </Card>

              {/* Phone */}
              <Card className="border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Phone className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2">Call Us</h3>
                  <p className="text-muted-foreground">
                    +201281008799<br />
                    +20 ************
                  </p>
                  <Button variant="outline" className="mt-4">
                    <Phone className="h-4 w-4 mr-2" />
                    Call Now
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ContactPage;
<img
  src={item.image}
  alt={item.title}
  className="w-full h-full object-cover"
/>
  </div >
  <CardContent className="p-6">
    <div className="flex items-center justify-between mb-2">
      <h3 className="text-lg font-bold text-foreground">{item.title}</h3>
      <Badge variant="secondary">{item.category}</Badge>
    </div>
    <p className="text-sm text-muted-foreground">{item.description}</p>
  </CardContent>
</Card >
  ))
}
      </div >
    </TabsContent >

  {/* Location Tab */ }
  < TabsContent value = "location" >
    <div className="text-center mb-12">
      <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
        Visit Our <span className="text-primary">Office</span>
      </h2>
      <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
        Located in the heart of Cairo, our office is easily accessible and equipped
        with modern facilities for client meetings and project consultations.
      </p>
    </div>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
      {/* Interactive Map */}
      <Card className="border-border">
        <CardContent className="p-8">
          <div className="aspect-video bg-muted rounded-lg flex items-center justify-center mb-6">
            <div className="text-center">
              <MapPin className="h-16 w-16 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Interactive Map</h3>
              <p className="text-muted-foreground mb-4">
                123 Construction Avenue, Cairo, Egypt 12345
              </p>
              <div className="flex gap-2 justify-center">
                <Button className="bg-gradient-primary hover:opacity-90">
                  <NavigationIcon className="mr-2 h-4 w-4" />
                  Get Directions
                </Button>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Download Map
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transportation Options */}
      <div className="space-y-6">
        <h3 className="text-2xl font-bold text-foreground mb-6">How to Reach Us</h3>
        {transportOptions.map((option, index) => (
          <Card key={index} className="border-border hover:shadow-primary transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-primary/10 rounded-lg">
                  <div className="text-primary">
                    {option.icon}
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground mb-1">{option.title}</h4>
                  <p className="text-sm text-muted-foreground mb-3">{option.description}</p>
                  <ul className="space-y-1">
                    {option.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="text-sm text-foreground flex items-start">
                        <CheckCircle className="h-3 w-3 text-primary mr-2 mt-0.5 flex-shrink-0" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  </TabsContent >
      </Tabs >
    </div >
      </section >

  {/* Enhanced Services Section */ }
  < section className = "py-20 bg-muted/50" >
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
          How Can We <span className="text-primary">Help You?</span>
        </h2>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Choose the type of inquiry that best matches your needs, and we'll connect you with the right expert.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {services.map((service, index) => (
          <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="text-white">
                  {service.icon}
                </div>
              </div>
              <h3 className="font-semibold text-foreground mb-2">{service.title}</h3>
              <p className="text-sm text-muted-foreground mb-3">{service.description}</p>
              <Badge variant="outline" className="text-xs">
                {service.responseTime}
              </Badge>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
      </section >

  {/* Call to Action */ }
  < section className = "py-20 bg-gradient-to-br from-primary/5 to-accent/5" >
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
        Ready to Start Your <span className="text-primary">Project?</span>
      </h2>
      <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
        Don't wait - contact us today and let our experts help you bring your MEP project to life.
        We're here to provide the solutions you need.
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-4xl mx-auto mb-8">
        <Card className="border-border">
          <CardContent className="p-6 text-center">
            <Phone className="h-8 w-8 text-primary mx-auto mb-3" />
            <h3 className="font-semibold text-foreground mb-2">Call Us Now</h3>
            <p className="text-sm text-muted-foreground mb-3">Speak directly with our experts</p>
            <Button variant="outline" size="sm" className="w-full">
              +20 (2) 123-4567
            </Button>
          </CardContent>
        </Card>

        <Card className="border-border">
          <CardContent className="p-6 text-center">
            <Mail className="h-8 w-8 text-primary mx-auto mb-3" />
            <h3 className="font-semibold text-foreground mb-2">Email Us</h3>
            <p className="text-sm text-muted-foreground mb-3">Get a detailed response</p>
            <Button variant="outline" size="sm" className="w-full">
              <EMAIL>
            </Button>
          </CardContent>
        </Card>

        <Card className="border-border">
          <CardContent className="p-6 text-center">
            <Calendar className="h-8 w-8 text-primary mx-auto mb-3" />
            <h3 className="font-semibold text-foreground mb-2">Schedule Meeting</h3>
            <p className="text-sm text-muted-foreground mb-3">Book a consultation</p>
            <Button variant="outline" size="sm" className="w-full">
              Book Now
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button size="lg" className="bg-gradient-primary hover:opacity-90 shadow-primary">
          <Send className="mr-2 h-5 w-5" />
          Send Message
        </Button>
        <Button size="lg" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
          <Download className="mr-2 h-5 w-5" />
          Download Brochure
        </Button>
        <Button size="lg" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
          <Eye className="mr-2 h-5 w-5" />
          View Portfolio
        </Button>
      </div>
    </div>
      </section >

  <Footer />
    </div >
  );
};

export default ContactPage;
