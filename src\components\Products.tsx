import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle, Star, Zap, Shield, Award } from 'lucide-react';

const Products = () => {
  const products = [
    {
      title: "Air Handling Unit",
      description: "Complete air treatment solutions for optimal indoor air quality and climate control",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop",
      category: "Air Handling",
      features: ["Energy Efficient", "Modular Design", "Smart Controls"],
      badge: "Popular",
      rating: 4.9,
      efficiency: "95%"
    },
    {
      title: "Condensing Unit",
      description: "High-efficiency cooling solutions for various commercial and industrial applications",
      image: "https://images.unsplash.com/photo-1524230572899-a752b3835840?w=400&h=250&fit=crop",
      category: "Cooling Systems",
      features: ["High Efficiency", "Weather Resistant", "Low Noise"],
      badge: "Best Seller",
      rating: 4.8,
      efficiency: "92%"
    },
    {
      title: "Heat Recovery Ventilation Unit",
      description: "Energy-efficient ventilation systems with advanced heat recovery technology",
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=400&h=250&fit=crop",
      category: "Ventilation",
      features: ["90% Heat Recovery", "Fresh Air Supply", "Energy Saving"],
      badge: "Eco-Friendly",
      rating: 4.9,
      efficiency: "90%"
    },
    {
      title: "Energy Recovery Ventilation Unit",
      description: "Advanced ERV systems for maximum energy efficiency and indoor air quality",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=250&fit=crop",
      category: "Ventilation",
      features: ["Total Energy Recovery", "Humidity Control", "Smart Operation"],
      badge: "Advanced",
      rating: 4.7,
      efficiency: "88%"
    },
    {
      title: "Fan Coil Unit",
      description: "Versatile heating and cooling units for optimal indoor comfort and climate control",
      image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=250&fit=crop",
      category: "Climate Control",
      features: ["Dual Function", "Quiet Operation", "Compact Design"],
      badge: "Versatile",
      rating: 4.6,
      efficiency: "85%"
    },
    {
      title: "Water Source Heat Pump",
      description: "Efficient heating and cooling systems using renewable water source energy",
      image: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=400&h=250&fit=crop",
      category: "Heat Pumps",
      features: ["Renewable Energy", "Year-Round Operation", "High COP"],
      badge: "Sustainable",
      rating: 4.8,
      efficiency: "93%"
    }
  ];

  return (
    <section id="products" className="py-20 bg-gradient-to-br from-background via-primary/5 to-accent/5 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-32 left-20 w-64 h-64 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-32 right-20 w-80 h-80 bg-accent rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full mb-6">
            <Star className="h-4 w-4 text-primary mr-2" />
            <span className="text-primary font-semibold text-sm uppercase tracking-wider">Premium MEP Solutions</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent leading-tight">
            Our Products
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Comprehensive MEP solutions engineered for excellence, efficiency, and reliability.
            Discover our range of premium HVAC systems designed to meet the highest industry standards.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {products.map((product, index) => (
            <Card key={index} className="group hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 hover:scale-[1.02] bg-card/50 backdrop-blur-sm border-border hover:border-primary/20 relative overflow-hidden">
              {/* Badge */}
              <div className="absolute top-4 left-4 z-10">
                <Badge
                  variant="secondary"
                  className={`
                    ${product.badge === 'Popular' ? 'bg-primary text-white' : ''}
                    ${product.badge === 'Best Seller' ? 'bg-accent text-white' : ''}
                    ${product.badge === 'Eco-Friendly' ? 'bg-green-500 text-white' : ''}
                    ${product.badge === 'Advanced' ? 'bg-purple-500 text-white' : ''}
                    ${product.badge === 'Versatile' ? 'bg-blue-500 text-white' : ''}
                    ${product.badge === 'Sustainable' ? 'bg-emerald-500 text-white' : ''}
                    font-semibold shadow-lg
                  `}
                >
                  {product.badge}
                </Badge>
              </div>

              {/* Efficiency Badge */}
              <div className="absolute top-4 right-4 z-10">
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1">
                  <Zap className="h-3 w-3 text-primary" />
                  <span className="text-xs font-semibold text-primary">{product.efficiency}</span>
                </div>
              </div>

              <div className="aspect-video overflow-hidden rounded-t-lg relative">
                <img
                  src={product.image}
                  alt={product.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              <CardHeader className="pb-4">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="text-xs">
                    {product.category}
                  </Badge>
                  <div className="flex items-center space-x-1">
                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    <span className="text-xs font-medium text-muted-foreground">{product.rating}</span>
                  </div>
                </div>
                <CardTitle className="text-xl text-foreground group-hover:text-primary transition-colors leading-tight">
                  {product.title}
                </CardTitle>
                <CardDescription className="text-muted-foreground leading-relaxed">
                  {product.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Features */}
                <div className="space-y-2 mb-6">
                  {product.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <CheckCircle className="h-3 w-3 text-primary flex-shrink-0" />
                      <span className="text-sm text-muted-foreground">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex-1 group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300 min-h-[44px]"
                  >
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                  <Button
                    size="sm"
                    className="bg-gradient-primary hover:opacity-90 shadow-lg px-4"
                  >
                    Quote
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Product Features Section */}
        <div className="bg-gradient-to-r from-primary/10 via-accent/5 to-primary/10 rounded-2xl p-8 mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
              Why Choose Our Products?
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Every product is engineered with precision and backed by our commitment to excellence
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Quality Assured</h4>
              <p className="text-muted-foreground">
                All products meet international standards with comprehensive quality testing
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Energy Efficient</h4>
              <p className="text-muted-foreground">
                Advanced technology ensures maximum efficiency and reduced operational costs
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Award Winning</h4>
              <p className="text-muted-foreground">
                Industry-recognized products with proven performance and reliability
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-foreground">
              Ready to Explore Our Complete Product Range?
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover detailed specifications, technical documentation, and find the perfect MEP solution for your project
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button size="lg" className="w-full sm:w-auto bg-gradient-primary hover:opacity-90 shadow-lg min-h-[44px] px-8">
                View All Products <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="w-full sm:w-auto min-h-[44px] px-8">
                Download Catalog
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Products;