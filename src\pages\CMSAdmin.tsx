import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  FileText,
  Users,
  Building2,
  Briefcase,
  MessageSquare,
  BarChart3,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  LogOut
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  getServices,
  getSolutions,
  getProjects,
  getTeamMembers,
  getTestimonials,
  getCompanyInfo,
  getSiteSettings
} from '@/utils/cmsUtils';
import CMSAuth from '@/components/cms/CMSAuth';

const CMSAdmin = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchQuery, setSearchQuery] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authError, setAuthError] = useState('');

  // Load data
  const servicesData = getServices();
  const solutionsData = getSolutions();
  const projectsData = getProjects();
  const teamData = getTeamMembers();
  const testimonialsData = getTestimonials();
  const companyData = getCompanyInfo();
  const settingsData = getSiteSettings();

  const stats = [
    {
      title: 'Services',
      count: servicesData.data?.length || 0,
      icon: <Briefcase className="h-6 w-6" />,
      color: 'bg-blue-500'
    },
    {
      title: 'Solutions',
      count: solutionsData.data?.length || 0,
      icon: <Building2 className="h-6 w-6" />,
      color: 'bg-green-500'
    },
    {
      title: 'Projects',
      count: projectsData.data?.length || 0,
      icon: <FileText className="h-6 w-6" />,
      color: 'bg-purple-500'
    },
    {
      title: 'Team Members',
      count: teamData.data?.length || 0,
      icon: <Users className="h-6 w-6" />,
      color: 'bg-orange-500'
    },
    {
      title: 'Testimonials',
      count: testimonialsData.data?.length || 0,
      icon: <MessageSquare className="h-6 w-6" />,
      color: 'bg-pink-500'
    }
  ];

  const ContentTable = ({ data, type, onEdit, onDelete, onView }: any) => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder={`Search ${type}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add {type}
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left p-4 font-medium">Title</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-left p-4 font-medium">Updated</th>
                  <th className="text-left p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {data?.map((item: any, index: number) => (
                  <tr key={item.id || index} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <div>
                        <div className="font-medium">{item.title || item.name}</div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {item.shortDescription || item.description || item.position}
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge variant={item.isActive ? 'default' : 'secondary'}>
                        {item.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="p-4 text-sm text-gray-500">
                      {new Date(item.updatedAt).toLocaleDateString()}
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" onClick={() => onView(item)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => onEdit(item)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => onDelete(item)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const handleEdit = (item: any) => {
    console.log('Edit:', item);
    // TODO: Open edit modal/form
  };

  const handleDelete = (item: any) => {
    console.log('Delete:', item);
    // TODO: Show confirmation dialog
  };

  const handleView = (item: any) => {
    console.log('View:', item);
    // TODO: Open view modal
  };

  const handleLogin = (credentials: { username: string; password: string }) => {
    // Simple demo authentication
    if (credentials.username === 'admin' && credentials.password === 'admin123') {
      setIsAuthenticated(true);
      setAuthError('');
    } else {
      setAuthError('Invalid username or password');
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setActiveTab('dashboard');
  };

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return <CMSAuth onLogin={handleLogin} error={authError} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">CMS Admin</h1>
              <p className="text-gray-600">Manage your website content</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Button>
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="services">Services</TabsTrigger>
            <TabsTrigger value="solutions">Solutions</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="testimonials">Testimonials</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {stats.map((stat, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold">{stat.count}</p>
                      </div>
                      <div className={`p-3 rounded-full ${stat.color} text-white`}>
                        {stat.icon}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="p-2 bg-blue-500 rounded-full text-white">
                      <FileText className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">New project added</p>
                      <p className="text-sm text-gray-500">Shopping Mall Complex - 2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="p-2 bg-green-500 rounded-full text-white">
                      <Users className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Team member updated</p>
                      <p className="text-sm text-gray-500">Sarah Mohamed profile - 5 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="p-2 bg-purple-500 rounded-full text-white">
                      <MessageSquare className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">New testimonial received</p>
                      <p className="text-sm text-gray-500">Ahmed Hassan - Grand Plaza Hotel - 1 day ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="services">
            <ContentTable
              data={servicesData.data}
              type="Service"
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
            />
          </TabsContent>

          <TabsContent value="solutions">
            <ContentTable
              data={solutionsData.data}
              type="Solution"
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
            />
          </TabsContent>

          <TabsContent value="projects">
            <ContentTable
              data={projectsData.data}
              type="Project"
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
            />
          </TabsContent>

          <TabsContent value="team">
            <ContentTable
              data={teamData.data}
              type="Team Member"
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
            />
          </TabsContent>

          <TabsContent value="testimonials">
            <ContentTable
              data={testimonialsData.data}
              type="Testimonial"
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
            />
          </TabsContent>

          <TabsContent value="settings">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Site Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Site Name</label>
                      <Input value={settingsData.data?.siteName} readOnly />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Site Description</label>
                      <Input value={settingsData.data?.siteDescription} readOnly />
                    </div>
                    <Button>Update Settings</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Company Name</label>
                      <Input value={companyData.data?.name} readOnly />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Founded Year</label>
                      <Input value={companyData.data?.foundedYear} readOnly />
                    </div>
                    <Button>Update Company Info</Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CMSAdmin;
