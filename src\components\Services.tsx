import {
  Zap,
  Droplets,
  Wind,
  Shield,
  Settings,
  Wrench,
  ThermometerSun,
  Activity,
  Phone,
  Download,
  CheckCircle,
  ArrowRight,
  Star,
  Award,
  Clock,
  Users,
  Target,
  Mail,
  Sparkles
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getServices } from '@/utils/strapiCmsUtils';

const Services = () => {
  const [cmsServices, setCmsServices] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);

  // Get services from CMS (Strapi or JSON fallback)
  React.useEffect(() => {
    const fetchServices = async () => {
      try {
        const servicesResponse = await getServices({ isActive: true });
        setCmsServices(servicesResponse.data || []);
      } catch (error) {
        console.error('Error fetching services:', error);
        setCmsServices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Icon mapping for CMS services
  const iconMap: { [key: string]: React.ReactNode } = {
    'Zap': <Zap className="h-8 w-8" />,
    'Wind': <Wind className="h-8 w-8" />,
    'Droplets': <Droplets className="h-8 w-8" />,
    'Flame': <Shield className="h-8 w-8" />,
    'Settings': <Settings className="h-8 w-8" />,
    'Wrench': <Wrench className="h-8 w-8" />
  };

  // Fallback services data (keeping original for backup)
  const fallbackServices = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Electrical Systems",
      description: "Complete electrical installation, power distribution, lighting systems, and emergency backup solutions for modern buildings.",
      features: ["Power Distribution", "Lighting Design", "Emergency Systems", "Smart Controls"],
      badge: "Core Service",
      complexity: "High",
      duration: "4-8 weeks",
      rating: 4.9
    },
    {
      icon: <Droplets className="h-8 w-8" />,
      title: "Plumbing Systems",
      description: "Comprehensive plumbing solutions including water supply, drainage, and specialized piping systems for all building types.",
      features: ["Water Supply", "Drainage Systems", "Fire Protection", "Medical Gas"],
      badge: "Essential",
      complexity: "Medium",
      duration: "3-6 weeks",
      rating: 4.8
    },
    {
      icon: <Wind className="h-8 w-8" />,
      title: "HVAC Systems",
      description: "Advanced heating, ventilation, and air conditioning systems for optimal indoor climate control and air quality.",
      features: ["Climate Control", "Ventilation", "Air Quality", "Energy Recovery"],
      badge: "Popular",
      complexity: "High",
      duration: "6-10 weeks",
      rating: 4.9
    },
    {
      icon: <ThermometerSun className="h-8 w-8" />,
      title: "Thermal Systems",
      description: "Efficient heating and cooling solutions including boilers, chillers, and heat pumps for energy-optimized buildings.",
      features: ["Boiler Systems", "Chiller Plants", "Heat Pumps", "Thermal Storage"],
      badge: "Specialized",
      complexity: "High",
      duration: "5-8 weeks",
      rating: 4.7
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Fire Protection",
      description: "Comprehensive fire safety systems including detection, suppression, and emergency response for maximum building safety.",
      features: ["Fire Detection", "Sprinkler Systems", "Suppression", "Emergency Egress"],
      badge: "Critical",
      complexity: "Medium",
      duration: "3-5 weeks",
      rating: 4.9
    },
    {
      icon: <Activity className="h-8 w-8" />,
      title: "Building Automation",
      description: "Smart building management systems for optimal performance, energy efficiency, and intelligent building operations.",
      features: ["BMS Integration", "Energy Management", "Remote Monitoring", "Predictive Maintenance"],
      badge: "Advanced",
      complexity: "High",
      duration: "4-7 weeks",
      rating: 4.8
    },
    {
      icon: <Settings className="h-8 w-8" />,
      title: "System Integration",
      description: "Seamless integration of all MEP systems for coordinated and efficient building operations with optimal performance.",
      features: ["System Coordination", "Performance Optimization", "Interface Management", "Testing & Commissioning"],
      badge: "Premium",
      complexity: "High",
      duration: "2-4 weeks",
      rating: 4.8
    },
    {
      icon: <Wrench className="h-8 w-8" />,
      title: "Maintenance Services",
      description: "Comprehensive maintenance and support services to ensure long-term system reliability and optimal performance.",
      features: ["Preventive Maintenance", "24/7 Support", "System Upgrades", "Performance Monitoring"],
      badge: "Ongoing",
      complexity: "Medium",
      duration: "Continuous",
      rating: 4.9
    }
  ];

  // Use CMS services if available, otherwise fallback to hardcoded data
  const services = cmsServices.length > 0 ? cmsServices.map(service => ({
    icon: iconMap[service.icon] || <Zap className="h-8 w-8" />,
    title: service.title,
    description: service.shortDescription,
    features: service.features,
    badge: service.badge,
    complexity: service.complexity,
    duration: service.duration,
    rating: service.rating
  })) : fallbackServices;

  return (
    <section id="services" className="py-20 bg-gradient-to-br from-muted/30 via-primary/5 to-accent/5 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-16 w-72 h-72 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-16 w-96 h-96 bg-accent rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full mb-6">
            <Award className="h-4 w-4 text-primary mr-2" />
            <span className="text-primary font-semibold text-sm uppercase tracking-wider">Professional MEP Services</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            Our <span className="bg-gradient-primary bg-clip-text text-transparent">Services</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Comprehensive MEP solutions designed to meet the complex demands of modern construction projects.
            From design to implementation, we deliver excellence in every aspect of building systems.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {services.map((service, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 hover:scale-[1.02] bg-card/50 backdrop-blur-sm border-border hover:border-primary/30 relative overflow-hidden"
            >
              {/* Badge */}
              <div className="absolute top-4 right-4 z-10">
                <Badge
                  variant="secondary"
                  className={`
                    ${service.badge === 'Core Service' ? 'bg-primary text-white' : ''}
                    ${service.badge === 'Essential' ? 'bg-blue-500 text-white' : ''}
                    ${service.badge === 'Popular' ? 'bg-accent text-white' : ''}
                    ${service.badge === 'Specialized' ? 'bg-purple-500 text-white' : ''}
                    ${service.badge === 'Critical' ? 'bg-red-500 text-white' : ''}
                    ${service.badge === 'Advanced' ? 'bg-emerald-500 text-white' : ''}
                    ${service.badge === 'Premium' ? 'bg-amber-500 text-white' : ''}
                    ${service.badge === 'Ongoing' ? 'bg-indigo-500 text-white' : ''}
                    font-semibold shadow-lg text-xs
                  `}
                >
                  {service.badge}
                </Badge>
              </div>

              <CardHeader className="text-center pb-4 pt-8">
                <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <div className="text-white">
                    {service.icon}
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors leading-tight">
                  {service.title}
                </CardTitle>

                {/* Rating */}
                <div className="flex items-center justify-center space-x-1 mt-2">
                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  <span className="text-xs font-medium text-muted-foreground">{service.rating}</span>
                </div>
              </CardHeader>

              <CardContent className="text-center px-6 pb-6">
                <p className="text-muted-foreground mb-6 leading-relaxed text-sm">
                  {service.description}
                </p>

                {/* Service Details */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground flex items-center">
                      <Target className="h-3 w-3 mr-1" />
                      Complexity
                    </span>
                    <span className={`font-medium ${service.complexity === 'High' ? 'text-red-500' :
                      service.complexity === 'Medium' ? 'text-yellow-500' : 'text-green-500'
                      }`}>
                      {service.complexity}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      Duration
                    </span>
                    <span className="font-medium text-primary">{service.duration}</span>
                  </div>
                </div>

                {/* Features */}
                <ul className="space-y-2 text-sm mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="text-muted-foreground flex items-center">
                      <CheckCircle className="h-3 w-3 text-primary mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                {/* Action Button */}
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300"
                >
                  Learn More
                  <ArrowRight className="ml-2 h-3 w-3 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Why Choose Our Services Section */}
        <div className="bg-gradient-to-r from-primary/10 via-accent/5 to-primary/10 rounded-2xl p-8 mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
              Why Choose Our MEP Services?
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Industry-leading expertise backed by proven results and client satisfaction
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Expert Team</h4>
              <p className="text-muted-foreground text-sm">
                Certified engineers with 15+ years of industry experience
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Precision Delivery</h4>
              <p className="text-muted-foreground text-sm">
                On-time project completion with meticulous attention to detail
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">Quality Assured</h4>
              <p className="text-muted-foreground text-sm">
                International standards compliance with comprehensive testing
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-foreground mb-2">24/7 Support</h4>
              <p className="text-muted-foreground text-sm">
                Continuous support and maintenance for optimal performance
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="relative bg-gradient-to-br from-primary/15 via-accent/10 to-primary/15 rounded-3xl p-8 md:p-12 text-center border border-primary/30 backdrop-blur-sm overflow-hidden">
          {/* Background Animation Elements */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-32 h-32 bg-primary rounded-full blur-2xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-40 h-40 bg-accent rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-primary rounded-full blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          </div>

          <div className="max-w-5xl mx-auto relative z-10">
            {/* Header Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-primary/20 rounded-full mb-6 backdrop-blur-sm">
              <Sparkles className="h-4 w-4 text-primary mr-2" />
              <span className="text-primary font-semibold text-sm uppercase tracking-wider">Transform Your Building</span>
            </div>

            <h3 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent leading-tight">
              Ready to Transform Your Building's MEP Systems?
            </h3>
            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-4xl mx-auto leading-relaxed">
              Let our expert engineers design and implement cutting-edge MEP solutions tailored to your specific needs.
              From concept to completion, we deliver excellence in every project with guaranteed satisfaction and ongoing support.
            </p>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button size="lg" className="group w-full sm:w-auto bg-gradient-primary hover:opacity-90 hover:scale-105 shadow-xl shadow-primary/30 min-h-[52px] px-10 transition-all duration-300 font-semibold">
                <Phone className="mr-2 h-5 w-5 group-hover:animate-bounce" />
                Get Free Consultation
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="group w-full sm:w-auto border-2 border-primary text-primary hover:bg-primary hover:text-white hover:scale-105 min-h-[52px] px-10 transition-all duration-300 font-semibold backdrop-blur-sm"
              >
                <Download className="mr-2 h-5 w-5 group-hover:animate-bounce" />
                Download Company Profile
              </Button>
            </div>

            {/* Contact Options */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8 max-w-2xl mx-auto">
              <div className="flex items-center justify-center space-x-4 p-6 bg-white/10 rounded-xl backdrop-blur-sm hover:bg-white/15 transition-all duration-300 cursor-pointer group">
                <div className="p-3 bg-gradient-primary rounded-full group-hover:scale-110 transition-transform shadow-lg">
                  <Phone className="h-5 w-5 text-white" />
                </div>
                <div className="text-left">
                  <div className="text-base font-semibold text-foreground">Call Us</div>
                  <div className="text-sm text-muted-foreground">+201281008799</div>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-4 p-6 bg-white/10 rounded-xl backdrop-blur-sm hover:bg-white/15 transition-all duration-300 cursor-pointer group">
                <div className="p-3 bg-gradient-accent rounded-full group-hover:scale-110 transition-transform shadow-lg">
                  <Mail className="h-5 w-5 text-white" />
                </div>
                <div className="text-left">
                  <div className="text-base font-semibold text-foreground">Email Us</div>
                  <div className="text-sm text-muted-foreground"><EMAIL></div>
                </div>
              </div>
            </div>

            {/* Enhanced Stats Section */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 pt-8 border-t border-border/30">
              <div className="group flex flex-col items-center space-y-3 p-6 bg-gradient-to-br from-white/10 to-white/5 rounded-xl backdrop-blur-sm hover:bg-white/15 transition-all duration-300 hover:scale-105">
                <div className="p-3 bg-gradient-primary rounded-full group-hover:scale-110 transition-transform shadow-lg">
                  <Award className="h-6 w-6 text-white" />
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">9+</div>
                  <div className="text-sm font-medium text-muted-foreground">Years Experience</div>
                </div>
              </div>
              <div className="group flex flex-col items-center space-y-3 p-6 bg-gradient-to-br from-white/10 to-white/5 rounded-xl backdrop-blur-sm hover:bg-white/15 transition-all duration-300 hover:scale-105">
                <div className="p-3 bg-gradient-accent rounded-full group-hover:scale-110 transition-transform shadow-lg">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">500+</div>
                  <div className="text-sm font-medium text-muted-foreground">Projects Completed</div>
                </div>
              </div>
              <div className="group flex flex-col items-center space-y-3 p-6 bg-gradient-to-br from-white/10 to-white/5 rounded-xl backdrop-blur-sm hover:bg-white/15 transition-all duration-300 hover:scale-105">
                <div className="p-3 bg-gradient-primary rounded-full group-hover:scale-110 transition-transform shadow-lg">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">100%</div>
                  <div className="text-sm font-medium text-muted-foreground">Client Satisfaction</div>
                </div>
              </div>
              <div className="group flex flex-col items-center space-y-3 p-6 bg-gradient-to-br from-white/10 to-white/5 rounded-xl backdrop-blur-sm hover:bg-white/15 transition-all duration-300 hover:scale-105">
                <div className="p-3 bg-gradient-accent rounded-full group-hover:scale-110 transition-transform shadow-lg">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">24/7</div>
                  <div className="text-sm font-medium text-muted-foreground">Support Available</div>
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-8 pt-6 border-t border-border/30">
              <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>ISO 9001 Certified</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Licensed Engineers</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Insured & Bonded</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Warranty Guaranteed</span>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </section>
  );
};

export default Services;