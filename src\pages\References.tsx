import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  MapPin,
  Calendar,
  Users,
  Award,
  Star,
  ExternalLink,
  Search,
  Filter,
  Grid,
  List,
  Eye,
  Download,
  Phone,
  Mail,
  CheckCircle,
  TrendingUp,
  Clock,
  DollarSign
} from 'lucide-react';
import { useState, useMemo } from 'react';

const References = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const projects = [
    {
      id: 1,
      title: "Grand Plaza Hotel",
      location: "Cairo, Egypt",
      category: "Hospitality",
      year: "2023",
      description: "Complete MEP installation for 300-room luxury hotel including HVAC, electrical, plumbing, and fire safety systems.",
      detailedDescription: "This prestigious project involved the complete MEP installation for a 300-room luxury hotel in the heart of Cairo. Our team delivered state-of-the-art HVAC systems, advanced electrical infrastructure, comprehensive plumbing solutions, and integrated fire safety systems. The project showcased our expertise in hospitality MEP solutions with a focus on guest comfort and energy efficiency.",
      scope: "Full MEP Installation",
      value: "$2.5M",
      duration: "18 months",
      client: "Grand Plaza Hotels Group",
      image: "/placeholder.svg",
      gallery: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
      features: ["Central HVAC System", "Guest Room Controls", "Kitchen Ventilation", "Pool Systems"],
      challenges: ["Complex coordination with interior design", "Tight construction schedule", "High-end finish requirements"],
      solutions: ["Advanced BIM modeling for coordination", "Prefabrication to accelerate installation", "Custom fabrication for aesthetic integration"],
      results: ["30% energy savings achieved", "99.9% system uptime", "Enhanced guest satisfaction scores", "LEED Gold certification"],
      technologies: ["Smart Building Automation", "Energy Recovery Systems", "Variable Refrigerant Flow", "IoT Monitoring"],
      certifications: ["LEED Gold", "ASHRAE Compliance", "Local Building Codes"]
    },
    {
      id: 2,
      title: "Cairo Medical Center",
      location: "New Cairo, Egypt",
      category: "Healthcare",
      year: "2023",
      description: "Specialized MEP systems for 200-bed hospital including medical gas, clean rooms, and emergency power systems.",
      detailedDescription: "A critical healthcare project involving the design and installation of specialized MEP systems for a 200-bed medical center. The project required strict adherence to healthcare standards and regulations, with systems designed for reliability, redundancy, and patient safety.",
      scope: "Medical MEP Systems",
      value: "$3.2M",
      duration: "24 months",
      client: "Cairo Healthcare Group",
      image: "/placeholder.svg",
      gallery: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
      features: ["Medical Gas Systems", "Operating Room HVAC", "Emergency Power", "Infection Control"],
      challenges: ["Strict regulatory compliance", "Zero downtime requirements", "Sterile environment maintenance"],
      solutions: ["Redundant system design", "Phased installation approach", "Advanced filtration systems"],
      results: ["100% regulatory compliance", "Zero system failures", "Improved patient outcomes", "24/7 reliable operation"],
      technologies: ["Medical Gas Distribution", "HEPA Filtration", "UPS Systems", "Building Management"],
      certifications: ["Healthcare Standards", "Medical Gas Compliance", "Emergency Systems Certified"]
    },
    {
      id: 3,
      title: "Pharma Manufacturing Plant",
      location: "6th of October City, Egypt",
      category: "Pharmaceutical",
      year: "2022",
      description: "Clean room MEP systems for pharmaceutical manufacturing facility with strict environmental controls.",
      detailedDescription: "State-of-the-art pharmaceutical manufacturing facility requiring precision environmental controls and regulatory compliance. Our team delivered clean room HVAC systems, process cooling, and compressed air systems meeting international pharmaceutical standards.",
      scope: "Clean Room Systems",
      value: "$4.1M",
      duration: "20 months",
      client: "Pharma Industries Ltd",
      image: "/placeholder.svg",
      gallery: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
      features: ["Clean Room HVAC", "Process Cooling", "Compressed Air", "Validation Support"],
      challenges: ["ISO 14644 compliance", "Contamination prevention", "Process validation"],
      solutions: ["Precision environmental control", "Validated systems design", "Comprehensive documentation"],
      results: ["FDA compliance achieved", "Zero contamination incidents", "Reduced production costs", "Validated operations"],
      technologies: ["Clean Room Technology", "Process Automation", "Environmental Monitoring", "Validation Systems"],
      certifications: ["ISO 14644", "FDA Compliance", "GMP Standards"]
    },
    {
      id: 4,
      title: "Business Tower Complex",
      location: "New Administrative Capital, Egypt",
      category: "Commercial",
      year: "2022",
      description: "Smart building MEP systems for 40-story office tower with advanced automation and energy management.",
      detailedDescription: "A landmark commercial project featuring a 40-story office tower with cutting-edge smart building technologies. The project integrated advanced building automation, energy management systems, and intelligent lighting controls to create a modern, efficient workspace.",
      scope: "Smart Building MEP",
      value: "$5.8M",
      duration: "30 months",
      client: "Capital Development Corp",
      image: "/placeholder.svg",
      gallery: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
      features: ["Building Automation", "Energy Management", "Smart Lighting", "Security Integration"],
      challenges: ["High-rise complexity", "Energy efficiency targets", "System integration"],
      solutions: ["Advanced BMS implementation", "Energy optimization algorithms", "Integrated control systems"],
      results: ["40% energy reduction", "LEED Platinum certification", "Improved tenant satisfaction", "Reduced operational costs"],
      technologies: ["IoT Integration", "AI-Powered Controls", "Energy Analytics", "Smart Sensors"],
      certifications: ["LEED Platinum", "Smart Building Certified", "Energy Star"]
    },
    {
      id: 5,
      title: "Food Processing Facility",
      location: "Alexandria, Egypt",
      category: "Food & Beverage",
      year: "2021",
      description: "Specialized MEP systems for food processing plant including cold storage and hygiene systems.",
      detailedDescription: "Large-scale food processing facility requiring specialized MEP systems to maintain strict hygiene standards and precise environmental controls. The project included cold storage systems, process ventilation, and comprehensive waste water treatment.",
      scope: "Food Grade Systems",
      value: "$1.8M",
      duration: "15 months",
      client: "Alexandria Food Industries",
      image: "/placeholder.svg",
      gallery: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
      features: ["Cold Storage Systems", "Process Ventilation", "Hygiene Systems", "Waste Water Treatment"],
      challenges: ["Food safety compliance", "Temperature control precision", "Hygiene maintenance"],
      solutions: ["Food-grade materials", "Precision refrigeration", "Automated cleaning systems"],
      results: ["Zero food safety incidents", "30% energy savings", "HACCP compliance", "Improved production efficiency"],
      technologies: ["Ammonia Refrigeration", "CIP Systems", "Process Automation", "Environmental Monitoring"],
      certifications: ["HACCP", "Food Safety Standards", "Environmental Compliance"]
    },
    {
      id: 6,
      title: "Shopping Mall Complex",
      location: "Giza, Egypt",
      category: "Retail",
      year: "2021",
      description: "Complete MEP installation for large shopping center including retail spaces, restaurants, and entertainment areas.",
      detailedDescription: "Comprehensive MEP installation for a major shopping center serving over 200 retail outlets, restaurants, and entertainment facilities. The project required diverse MEP solutions to accommodate various tenant requirements while maintaining overall system efficiency.",
      scope: "Retail MEP Systems",
      value: "$3.7M",
      duration: "22 months",
      client: "Giza Mall Development",
      image: "/placeholder.svg",
      gallery: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
      features: ["Retail HVAC", "Escalator Systems", "Fire Safety", "Parking Ventilation"],
      challenges: ["Diverse tenant requirements", "High foot traffic areas", "Complex fire safety"],
      solutions: ["Flexible HVAC zones", "Advanced fire suppression", "Efficient ventilation design"],
      results: ["Optimal shopping comfort", "25% energy savings", "Enhanced safety systems", "Improved air quality"],
      technologies: ["Variable Air Volume", "Fire Detection Systems", "Parking Ventilation", "Energy Management"],
      certifications: ["Fire Safety Compliance", "Building Codes", "Energy Efficiency"]
    }
  ];

  const categories = ['all', 'Hospitality', 'Healthcare', 'Pharmaceutical', 'Commercial', 'Food & Beverage', 'Retail'];

  const filteredProjects = useMemo(() => {
    let filtered = projects;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.location.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.features.some(feature => feature.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, [searchQuery, selectedCategory, projects]);

  const stats = [
    { icon: <Building2 className="h-8 w-8" />, value: "200+", label: "Projects Completed" },
    { icon: <Users className="h-8 w-8" />, value: "150+", label: "Satisfied Clients" },
    { icon: <Award className="h-8 w-8" />, value: "15+", label: "Years Experience" },
    { icon: <Star className="h-8 w-8" />, value: "98%", label: "Client Satisfaction" }
  ];

  const testimonials = [
    {
      id: 1,
      name: "Ahmed Hassan",
      position: "Project Manager",
      company: "Grand Plaza Hotel",
      quote: "Nile Pro delivered exceptional MEP solutions for our hotel. Their attention to detail and professional approach exceeded our expectations. The systems have been running flawlessly since installation.",
      rating: 5,
      image: "/placeholder.svg",
      projectValue: "$2.5M",
      completionYear: "2023"
    },
    {
      id: 2,
      name: "Dr. Sarah Mohamed",
      position: "Facility Director",
      company: "Cairo Medical Center",
      quote: "The medical MEP systems installed by Nile Pro meet all international standards. Their expertise in healthcare facilities is outstanding, and their support team is always available when needed.",
      rating: 5,
      image: "/placeholder.svg",
      projectValue: "$3.2M",
      completionYear: "2023"
    },
    {
      id: 3,
      name: "Mahmoud Ali",
      position: "Operations Manager",
      company: "Business Tower Complex",
      quote: "The smart building systems implemented by Nile Pro have significantly improved our energy efficiency and operational costs. The ROI has exceeded our projections.",
      rating: 5,
      image: "/placeholder.svg",
      projectValue: "$5.8M",
      completionYear: "2022"
    },
    {
      id: 4,
      name: "Eng. Fatima Khalil",
      position: "Technical Director",
      company: "Pharma Industries Ltd",
      quote: "Nile Pro's expertise in pharmaceutical MEP systems is unmatched. They delivered a fully validated clean room facility that meets all FDA requirements.",
      rating: 5,
      image: "/placeholder.svg",
      projectValue: "$4.1M",
      completionYear: "2022"
    },
    {
      id: 5,
      name: "Omar Mansour",
      position: "Facility Manager",
      company: "Alexandria Food Industries",
      quote: "The food-grade MEP systems installed by Nile Pro have helped us maintain the highest hygiene standards while reducing our operational costs significantly.",
      rating: 5,
      image: "/placeholder.svg",
      projectValue: "$1.8M",
      completionYear: "2021"
    },
    {
      id: 6,
      name: "Layla Abdel Rahman",
      position: "Mall Operations Director",
      company: "Giza Mall Development",
      quote: "Nile Pro's comprehensive MEP solutions have created a comfortable shopping environment for our customers while keeping our energy costs under control.",
      rating: 5,
      image: "/placeholder.svg",
      projectValue: "$3.7M",
      completionYear: "2021"
    }
  ];

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Our References
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Explore our portfolio of successful MEP projects across various industries.
              Each project showcases our commitment to excellence and innovation.
            </p>

            {/* Search and Filter Controls */}
            <div className="max-w-4xl mx-auto mb-12">
              <div className="flex flex-col lg:flex-row gap-4 mb-6">
                {/* Search Bar */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search projects by name, location, or features..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 min-h-[44px]"
                  />
                </div>

                {/* View Mode Toggle */}
                <div className="flex border border-border rounded-md overflow-hidden">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-none min-h-[44px]"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-none min-h-[44px]"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Category Tabs */}
              <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
                <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 min-h-[44px]">
                  {categories.map((category) => (
                    <TabsTrigger key={category} value={category}>
                      {category === 'all' ? 'All Projects' : category}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="text-white">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Featured <span className="text-primary">Projects</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              A selection of our most significant MEP projects demonstrating our expertise across different sectors.
            </p>

            {/* Results Summary */}
            <div className="text-center mb-8">
              <p className="text-muted-foreground">
                Showing {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''}
                {searchQuery && ` for "${searchQuery}"`}
                {selectedCategory !== 'all' && ` in ${selectedCategory}`}
              </p>
            </div>
          </div>

          {/* Projects Display */}
          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8"
            : "space-y-8"
          }>
            {filteredProjects.map((project) => (
              <Card key={project.id} className={`group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] border-border ${viewMode === 'list' ? 'flex flex-col lg:flex-row' : ''
                }`}>
                <div className={`overflow-hidden ${viewMode === 'list'
                  ? 'lg:w-1/3 aspect-video lg:aspect-square rounded-l-lg'
                  : 'aspect-video rounded-t-lg'
                  }`}>
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                </div>

                <div className={viewMode === 'list' ? 'lg:w-2/3 flex flex-col' : ''}>
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary">{project.category}</Badge>
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {project.year}
                      </span>
                    </div>
                    <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                      {project.title}
                    </CardTitle>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4 mr-1" />
                      {project.location}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4 flex-1">
                    <p className="text-muted-foreground text-sm">
                      {viewMode === 'list' ? project.detailedDescription : project.description}
                    </p>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 text-primary mr-2" />
                        <span className="text-foreground font-medium">{project.value}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-primary mr-2" />
                        <span className="text-foreground font-medium">{project.duration}</span>
                      </div>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 text-primary mr-2" />
                        <span className="text-foreground font-medium">{project.client}</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="h-4 w-4 text-primary mr-2" />
                        <span className="text-foreground font-medium">{project.scope}</span>
                      </div>
                    </div>

                    {viewMode === 'list' && (
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-foreground text-sm mb-2">Key Results:</h4>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                            {project.results.slice(0, 4).map((result, idx) => (
                              <div key={idx} className="flex items-start text-xs text-muted-foreground">
                                <CheckCircle className="h-3 w-3 text-primary mr-1 mt-0.5 flex-shrink-0" />
                                {result}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="space-y-2">
                      <h4 className="font-semibold text-foreground text-sm">Key Features:</h4>
                      <div className="flex flex-wrap gap-1">
                        {project.features.map((feature, featureIndex) => (
                          <Badge key={featureIndex} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2 pt-4">
                      <Button variant="outline" className="flex-1 group border-primary text-primary hover:bg-primary hover:text-white min-h-[44px]">
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm" className="min-h-[44px]">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>

          {/* No Results */}
          {filteredProjects.length === 0 && (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <h3 className="text-lg font-semibold mb-2">No projects found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or category filter.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Enhanced Testimonials */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Client <span className="text-primary">Testimonials</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Hear what our clients say about our MEP solutions and services. Real feedback from real projects.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {testimonials.map((testimonial) => (
              <Card key={testimonial.id} className="border-border hover:shadow-primary transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-4 bg-muted">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex mb-2">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" />
                        {testimonial.completionYear}
                      </div>
                    </div>
                  </div>

                  <p className="text-muted-foreground mb-4 italic text-sm leading-relaxed">
                    "{testimonial.quote}"
                  </p>

                  <div className="border-t border-border pt-4">
                    <div className="font-semibold text-foreground">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.position}</div>
                    <div className="text-sm text-primary font-medium">{testimonial.company}</div>

                    <div className="flex items-center justify-between mt-3 text-xs">
                      <span className="text-muted-foreground">Project Value:</span>
                      <span className="font-bold text-primary">{testimonial.projectValue}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
            Ready to Start Your <span className="text-primary">Next Project?</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Join our growing list of satisfied clients. Contact us today to discuss your MEP requirements
            and discover how we can bring your vision to life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-gradient-primary hover:opacity-90 shadow-primary">
              <Phone className="mr-2 h-5 w-5" />
              Request Consultation
            </Button>
            <Button size="lg" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
              <Mail className="mr-2 h-5 w-5" />
              Get Quote
            </Button>
            <Button size="lg" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
              <Download className="mr-2 h-5 w-5" />
              Download Portfolio
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default References;
