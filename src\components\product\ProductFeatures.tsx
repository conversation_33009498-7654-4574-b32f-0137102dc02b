import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>ircle, Zap, Shield, Settings, Wrench } from 'lucide-react';
import { ProductFeature } from '@/types/product';

interface ProductFeaturesProps {
  features: ProductFeature[];
  title?: string;
  layout?: 'list' | 'grid' | 'cards';
  showIcons?: boolean;
  className?: string;
}

const iconMap: Record<string, any> = {
  'Zap': Zap,
  'Shield': Shield,
  'Settings': Settings,
  'Wrench': Wrench,
  'CheckCircle': CheckCircle,
};

const ProductFeatures = ({ 
  features, 
  title = "Key Features",
  layout = 'list',
  showIcons = true,
  className = ""
}: ProductFeaturesProps) => {
  const getIcon = (iconName?: string) => {
    if (!showIcons || !iconName) return CheckCircle;
    return iconMap[iconName] || CheckCircle;
  };

  if (layout === 'cards') {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-primary">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {features.map((feature, index) => {
              const IconComponent = getIcon(feature.icon);
              return (
                <div key={index} className="p-4 bg-muted rounded-lg">
                  <div className="flex items-start space-x-3">
                    <IconComponent className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-1">{feature.title}</h3>
                      <p className="text-sm text-muted-foreground">{feature.description}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (layout === 'grid') {
    return (
      <div className={className}>
        <h2 className="text-2xl font-bold mb-6 text-primary">{title}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => {
            const IconComponent = getIcon(feature.icon);
            return (
              <div key={index} className="text-center p-6 bg-card rounded-lg border">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <IconComponent className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <h3 className="font-semibold mb-2">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // Default list layout
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-primary">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {features.map((feature, index) => {
            const IconComponent = getIcon(feature.icon);
            return (
              <div key={index} className="flex items-start space-x-3">
                <IconComponent className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold mb-1">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductFeatures;
