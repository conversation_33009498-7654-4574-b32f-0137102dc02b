import { Service } from '@/types/cms';

export const services: Service[] = [
  {
    id: 'service-001',
    title: 'Electrical Systems',
    slug: 'electrical-systems',
    shortDescription: 'Complete electrical installation, power distribution, lighting systems, and emergency backup solutions for modern buildings.',
    fullDescription: 'Our electrical systems services encompass comprehensive electrical installation, power distribution, advanced lighting design, and reliable emergency backup solutions. We specialize in modern building electrical infrastructure that meets international standards and ensures optimal performance, safety, and energy efficiency.',
    icon: 'Zap',
    features: [
      'Power Distribution',
      'Lighting Design',
      'Emergency Systems',
      'Smart Controls',
      'Energy Management',
      'Safety Systems'
    ],
    badge: 'Core Service',
    complexity: 'High',
    duration: '4-8 weeks',
    rating: 4.9,
    images: [
      {
        url: '/placeholder.svg',
        alt: 'Electrical Systems Installation',
        caption: 'Professional electrical systems installation',
        type: 'image'
      }
    ],
    category: 'electrical',
    tags: ['Electrical', 'Power', 'Lighting', 'Emergency', 'Smart Systems'],
    isActive: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  },
  {
    id: 'service-002',
    title: 'HVAC Systems',
    slug: 'hvac-systems',
    shortDescription: 'Advanced heating, ventilation, and air conditioning solutions for optimal indoor climate control and air quality.',
    fullDescription: 'Our HVAC systems provide comprehensive climate control solutions including heating, ventilation, and air conditioning. We design and install energy-efficient systems that maintain optimal indoor air quality, temperature control, and humidity levels for maximum comfort and operational efficiency.',
    icon: 'Wind',
    features: [
      'Climate Control',
      'Air Quality Management',
      'Energy Efficiency',
      'Smart Thermostats',
      'Ventilation Systems',
      'Maintenance Programs'
    ],
    badge: 'Essential',
    complexity: 'High',
    duration: '6-12 weeks',
    rating: 4.8,
    images: [
      {
        url: '/placeholder.svg',
        alt: 'HVAC Systems Installation',
        caption: 'Modern HVAC systems for optimal climate control',
        type: 'image'
      }
    ],
    category: 'hvac',
    tags: ['HVAC', 'Climate Control', 'Air Quality', 'Ventilation', 'Energy Efficient'],
    isActive: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  },
  {
    id: 'service-003',
    title: 'Plumbing Systems',
    slug: 'plumbing-systems',
    shortDescription: 'Comprehensive plumbing solutions including water supply, drainage, and specialized piping systems for all building types.',
    fullDescription: 'Our plumbing systems services cover complete water supply, drainage, and specialized piping solutions. We provide reliable, efficient plumbing infrastructure that ensures proper water distribution, waste management, and compliance with health and safety regulations.',
    icon: 'Droplets',
    features: [
      'Water Supply Systems',
      'Drainage Solutions',
      'Pipe Installation',
      'Leak Detection',
      'Water Treatment',
      'Emergency Repairs'
    ],
    badge: 'Popular',
    complexity: 'Medium',
    duration: '3-6 weeks',
    rating: 4.7,
    images: [
      {
        url: '/placeholder.svg',
        alt: 'Plumbing Systems Installation',
        caption: 'Professional plumbing systems and water management',
        type: 'image'
      }
    ],
    category: 'plumbing',
    tags: ['Plumbing', 'Water Supply', 'Drainage', 'Piping', 'Water Treatment'],
    isActive: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  },
  {
    id: 'service-004',
    title: 'Fire Safety Systems',
    slug: 'fire-safety-systems',
    shortDescription: 'Complete fire detection, suppression, and safety systems to protect lives and property in all building types.',
    fullDescription: 'Our fire safety systems provide comprehensive protection through advanced fire detection, suppression, and emergency response systems. We design and install integrated fire safety solutions that comply with international standards and ensure maximum protection for occupants and property.',
    icon: 'Flame',
    features: [
      'Fire Detection',
      'Suppression Systems',
      'Emergency Lighting',
      'Smoke Management',
      'Alarm Systems',
      'Compliance Certification'
    ],
    badge: 'Specialized',
    complexity: 'High',
    duration: '4-8 weeks',
    rating: 4.9,
    images: [
      {
        url: '/placeholder.svg',
        alt: 'Fire Safety Systems',
        caption: 'Advanced fire safety and suppression systems',
        type: 'image'
      }
    ],
    category: 'electrical',
    tags: ['Fire Safety', 'Detection', 'Suppression', 'Emergency', 'Compliance'],
    isActive: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  },
  {
    id: 'service-005',
    title: 'Building Automation',
    slug: 'building-automation',
    shortDescription: 'Smart building automation systems for integrated control of HVAC, lighting, security, and energy management.',
    fullDescription: 'Our building automation systems integrate all building systems into a centralized, intelligent control platform. We provide smart solutions that optimize energy efficiency, enhance occupant comfort, improve security, and reduce operational costs through automated monitoring and control.',
    icon: 'Settings',
    features: [
      'Centralized Control',
      'Energy Optimization',
      'Smart Monitoring',
      'Remote Access',
      'Predictive Maintenance',
      'Integration Capabilities'
    ],
    badge: 'Advanced',
    complexity: 'High',
    duration: '8-16 weeks',
    rating: 4.8,
    images: [
      {
        url: '/placeholder.svg',
        alt: 'Building Automation Systems',
        caption: 'Smart building automation and control systems',
        type: 'image'
      }
    ],
    category: 'automation',
    tags: ['Automation', 'Smart Building', 'Control Systems', 'Energy Management', 'IoT'],
    isActive: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  },
  {
    id: 'service-006',
    title: 'Maintenance Services',
    slug: 'maintenance-services',
    shortDescription: 'Comprehensive maintenance and support services to ensure optimal performance and longevity of MEP systems.',
    fullDescription: 'Our maintenance services provide ongoing support and preventive maintenance for all MEP systems. We offer scheduled maintenance programs, emergency repairs, system upgrades, and performance optimization to ensure your building systems operate at peak efficiency throughout their lifecycle.',
    icon: 'Wrench',
    features: [
      'Preventive Maintenance',
      'Emergency Repairs',
      'System Upgrades',
      'Performance Monitoring',
      '24/7 Support',
      'Warranty Services'
    ],
    badge: 'Essential',
    complexity: 'Medium',
    duration: 'Ongoing',
    rating: 4.7,
    images: [
      {
        url: '/placeholder.svg',
        alt: 'Maintenance Services',
        caption: 'Professional maintenance and support services',
        type: 'image'
      }
    ],
    category: 'maintenance',
    tags: ['Maintenance', 'Support', 'Repairs', 'Upgrades', 'Monitoring'],
    isActive: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  }
];

export const serviceCategories = [
  {
    id: 'electrical' as const,
    name: 'Electrical Systems',
    description: 'Power distribution, lighting, and electrical infrastructure',
    icon: 'Zap'
  },
  {
    id: 'mechanical' as const,
    name: 'Mechanical Systems',
    description: 'HVAC, ventilation, and mechanical equipment',
    icon: 'Cog'
  },
  {
    id: 'plumbing' as const,
    name: 'Plumbing Systems',
    description: 'Water supply, drainage, and plumbing infrastructure',
    icon: 'Droplets'
  },
  {
    id: 'hvac' as const,
    name: 'HVAC Systems',
    description: 'Heating, ventilation, and air conditioning',
    icon: 'Wind'
  },
  {
    id: 'automation' as const,
    name: 'Building Automation',
    description: 'Smart building controls and automation systems',
    icon: 'Settings'
  },
  {
    id: 'maintenance' as const,
    name: 'Maintenance Services',
    description: 'Ongoing maintenance and support services',
    icon: 'Wrench'
  }
];
