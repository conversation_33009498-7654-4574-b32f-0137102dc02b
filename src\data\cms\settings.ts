import { SiteSettings } from '@/types/cms';

export const siteSettings: SiteSettings = {
  id: 'settings-001',
  siteName: 'Nile Pro MEP',
  siteDescription: 'Leading MEP construction specialists delivering exceptional mechanical, electrical, and plumbing solutions for commercial and industrial projects across Egypt.',
  siteKeywords: [
    'MEP',
    'construction',
    'electrical',
    'plumbing',
    'HVAC',
    'Egypt',
    'engineering',
    'building automation',
    'mechanical systems',
    'fire safety',
    'sustainable building',
    'energy efficiency'
  ],
  logo: '/logo.png',
  favicon: '/logo.png',
  contactInfo: {
    phones: ['+201281008799', '+20 ************'],
    emails: ['<EMAIL>', '<EMAIL>'],
    address: {
      street: 'El-Nozha District',
      city: 'Cairo',
      state: 'Cairo Governorate',
      country: 'Egypt',
      postalCode: '11371',
      coordinates: {
        lat: 30.0444,
        lng: 31.2357
      }
    },
    whatsapp: '+20 ************'
  },
  socialMedia: {
    facebook: 'https://facebook.com/nileproMEP',
    twitter: 'https://twitter.com/nileproMEP',
    linkedin: 'https://linkedin.com/company/nile-pro-mep',
    instagram: 'https://instagram.com/nileproMEP',
    youtube: 'https://youtube.com/@nileproMEP'
  },
  businessHours: [
    {
      day: 'Monday',
      open: '08:00',
      close: '18:00',
      closed: false
    },
    {
      day: 'Tuesday',
      open: '08:00',
      close: '18:00',
      closed: false
    },
    {
      day: 'Wednesday',
      open: '08:00',
      close: '18:00',
      closed: false
    },
    {
      day: 'Thursday',
      open: '08:00',
      close: '18:00',
      closed: false
    },
    {
      day: 'Friday',
      open: '09:00',
      close: '16:00',
      closed: false
    },
    {
      day: 'Saturday',
      open: '09:00',
      close: '14:00',
      closed: false
    },
    {
      day: 'Sunday',
      open: '00:00',
      close: '00:00',
      closed: true
    }
  ],
  seoSettings: {
    defaultTitle: 'Nile Pro MEP - Engineering Excellence in Construction',
    defaultDescription: 'Leading MEP construction specialists delivering exceptional mechanical, electrical, and plumbing solutions for commercial and industrial projects across Egypt.',
    defaultKeywords: [
      'MEP contractor Egypt',
      'HVAC installation Cairo',
      'electrical systems Egypt',
      'plumbing contractor Cairo',
      'building automation Egypt',
      'fire safety systems',
      'sustainable building Egypt',
      'energy efficient MEP'
    ],
    ogImage: '/logo.png',
    twitterHandle: '@nileproMEP',
    googleAnalyticsId: 'GA-XXXXXXXXX',
    googleTagManagerId: 'GTM-XXXXXXX'
  },
  maintenanceMode: false,
  analytics: {
    googleAnalyticsId: 'GA-XXXXXXXXX',
    googleTagManagerId: 'GTM-XXXXXXX',
    facebookPixelId: 'FB-XXXXXXXXX',
    enabled: true
  },
  isActive: true,
  createdAt: '2024-01-01',
  updatedAt: '2024-01-15'
};

// Additional site configuration
export const siteConfig = {
  navigation: {
    mainMenu: [
      { name: 'Home', href: '/', active: true },
      { name: 'Corporate', href: '/corporate', active: true },
      { 
        name: 'Products', 
        href: '/products', 
        active: true,
        hasDropdown: true,
        dropdownItems: [
          { name: 'All Products', href: '/products' },
          { name: 'Air Handling Units', href: '/products/air-handling-unit' },
          { name: 'Condensing Units', href: '/products/condensing-unit' },
          { name: 'Heat Recovery Units', href: '/products/heat-recovery-ventilation-unit' },
          { name: 'Fan Coil Units', href: '/products/fan-coil-unit' },
          { name: 'Heat Pumps', href: '/products/water-source-heat-pump' }
        ]
      },
      { 
        name: 'Solutions', 
        href: '/solutions', 
        active: true,
        hasDropdown: true,
        dropdownItems: [
          { name: 'All Solutions', href: '/solutions' },
          { name: 'Hospitality', href: '/solutions/hospitality' },
          { name: 'Healthcare', href: '/solutions/healthcare' },
          { name: 'Industrial', href: '/solutions/industrial' },
          { name: 'Office Buildings', href: '/solutions/office' }
        ]
      },
      { name: 'References', href: '/references', active: true },
      { name: 'Contact', href: '/contact', active: true }
    ],
    footerMenu: {
      services: [
        { name: 'Electrical Systems', href: '/services/electrical-systems' },
        { name: 'HVAC Systems', href: '/services/hvac-systems' },
        { name: 'Plumbing Systems', href: '/services/plumbing-systems' },
        { name: 'Fire Safety', href: '/services/fire-safety-systems' },
        { name: 'Building Automation', href: '/services/building-automation' },
        { name: 'Maintenance', href: '/services/maintenance-services' }
      ],
      quickLinks: [
        { name: 'About Us', href: '/corporate' },
        { name: 'Our Team', href: '/corporate#team' },
        { name: 'Careers', href: '/careers' },
        { name: 'News & Updates', href: '/news' },
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'Terms of Service', href: '/terms' }
      ]
    }
  },
  features: {
    newsletter: {
      enabled: true,
      title: 'Stay Updated',
      description: 'Subscribe to our newsletter for the latest MEP industry insights and company updates.'
    },
    whatsappFloat: {
      enabled: true,
      number: '+20 ************',
      message: 'Hello! I would like to inquire about your MEP services.'
    },
    cookieConsent: {
      enabled: true,
      message: 'We use cookies to enhance your browsing experience and provide personalized content.',
      acceptText: 'Accept All',
      declineText: 'Decline',
      settingsText: 'Cookie Settings'
    }
  },
  branding: {
    colors: {
      primary: '#1e40af', // Blue
      secondary: '#f59e0b', // Gold/Amber
      accent: '#dc2626', // Red
      neutral: '#6b7280' // Gray
    },
    fonts: {
      heading: 'Inter',
      body: 'Inter'
    }
  }
};
