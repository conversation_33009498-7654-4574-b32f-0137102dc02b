# Strapi Integration Guide for Nile Pro MEP

This guide explains how to integrate Strapi CMS with the Nile Pro MEP website for dynamic product management.

## Overview

Currently, the website uses static product data defined in React components. This guide will help you migrate to a dynamic Strapi-based system for better content management.

## Current Product Structure

The current product data structure includes:

```typescript
interface Product {
  id: string;
  brandId: string;
  name: string;
  model: string;
  category: string;
  image: string;
  description: string;
  specifications: Record<string, string>;
  features: string[];
  availability: string;
  rating: number;
  reviews: number;
  featured: boolean;
  price?: number;
  currency?: string;
}

interface Brand {
  id: string;
  name: string;
  logo: string;
  description: string;
  country: string;
  founded: string;
  specialties: string[];
  website: string;
  productCount: number;
  featured: boolean;
  fullDescription?: string;
  headquarters?: string;
  employees?: string;
  markets?: string[];
  certifications?: string[];
  contact?: {
    phone: string;
    email: string;
    address: string;
  };
}
```

## Strapi Setup

### 1. Install Strapi

```bash
npx create-strapi-app@latest nile-pro-cms --quickstart
cd nile-pro-cms
npm run develop
```

### 2. Create Content Types

#### Brand Content Type

Create a new content type called `Brand` with these fields:

- **Text Fields:**
  - `name` (Short text, required)
  - `description` (Long text, required)
  - `country` (Short text, required)
  - `founded` (Short text, required)
  - `website` (Short text, required)
  - `headquarters` (Short text)
  - `employees` (Short text)
  - `fullDescription` (Rich text)

- **Media Field:**
  - `logo` (Single media, required)

- **JSON Fields:**
  - `specialties` (JSON)
  - `markets` (JSON)
  - `certifications` (JSON)
  - `contact` (JSON)

- **Boolean Field:**
  - `featured` (Boolean, default: false)

- **Number Field:**
  - `productCount` (Integer, default: 0)

#### Product Content Type

Create a new content type called `Product` with these fields:

- **Text Fields:**
  - `name` (Short text, required)
  - `model` (Short text, required)
  - `description` (Long text, required)
  - `category` (Enumeration: air-handling-unit, condensing-unit, heat-recovery-ventilation-unit, energy-recovery-ventilation-unit, fan-coil-unit, ecology-unit, water-source-heat-pump, exhaust-unit)
  - `availability` (Enumeration: in-stock, pre-order, out-of-stock)
  - `currency` (Short text, default: USD)

- **Media Field:**
  - `image` (Single media, required)

- **Number Fields:**
  - `price` (Decimal)
  - `rating` (Decimal, min: 0, max: 5)
  - `reviews` (Integer, default: 0)

- **Boolean Field:**
  - `featured` (Boolean, default: false)

- **JSON Fields:**
  - `specifications` (JSON)
  - `features` (JSON)

- **Relation Field:**
  - `brand` (Many-to-One relation with Brand)

### 3. Configure API Permissions

1. Go to Settings > Users & Permissions Plugin > Roles
2. Edit the Public role
3. Enable the following permissions:
   - **Brand**: find, findOne
   - **Product**: find, findOne

## Frontend Integration

### 1. Install Required Packages

```bash
npm install @strapi/strapi axios
```

### 2. Create API Service

Create `src/services/strapiApi.ts`:

```typescript
import axios from 'axios';

const STRAPI_URL = process.env.REACT_APP_STRAPI_URL || 'http://localhost:1337';

const api = axios.create({
  baseURL: `${STRAPI_URL}/api`,
});

export interface StrapiProduct {
  id: number;
  attributes: {
    name: string;
    model: string;
    description: string;
    category: string;
    availability: string;
    price?: number;
    currency: string;
    rating: number;
    reviews: number;
    featured: boolean;
    specifications: Record<string, string>;
    features: string[];
    image: {
      data: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    brand: {
      data: {
        id: number;
        attributes: {
          name: string;
          // ... other brand fields
        };
      };
    };
  };
}

export interface StrapiBrand {
  id: number;
  attributes: {
    name: string;
    description: string;
    country: string;
    founded: string;
    website: string;
    featured: boolean;
    productCount: number;
    specialties: string[];
    markets: string[];
    certifications: string[];
    logo: {
      data: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    // ... other fields
  };
}

export const strapiApi = {
  // Get all brands
  getBrands: async (): Promise<StrapiBrand[]> => {
    const response = await api.get('/brands?populate=*');
    return response.data.data;
  },

  // Get single brand
  getBrand: async (id: string): Promise<StrapiBrand> => {
    const response = await api.get(`/brands/${id}?populate=*`);
    return response.data.data;
  },

  // Get all products
  getProducts: async (): Promise<StrapiProduct[]> => {
    const response = await api.get('/products?populate=*');
    return response.data.data;
  },

  // Get products by brand
  getProductsByBrand: async (brandId: string): Promise<StrapiProduct[]> => {
    const response = await api.get(`/products?filters[brand][id][$eq]=${brandId}&populate=*`);
    return response.data.data;
  },

  // Get featured products
  getFeaturedProducts: async (): Promise<StrapiProduct[]> => {
    const response = await api.get('/products?filters[featured][$eq]=true&populate=*');
    return response.data.data;
  },

  // Search products
  searchProducts: async (query: string): Promise<StrapiProduct[]> => {
    const response = await api.get(`/products?filters[$or][0][name][$containsi]=${query}&filters[$or][1][description][$containsi]=${query}&populate=*`);
    return response.data.data;
  },
};
```

### 3. Create Data Transformation Utilities

Create `src/utils/strapiTransform.ts`:

```typescript
import { StrapiProduct, StrapiBrand } from '../services/strapiApi';

export const transformStrapiProduct = (strapiProduct: StrapiProduct) => {
  const { attributes } = strapiProduct;
  
  return {
    id: strapiProduct.id.toString(),
    brandId: attributes.brand.data.id.toString(),
    name: attributes.name,
    model: attributes.model,
    category: attributes.category,
    image: `${process.env.REACT_APP_STRAPI_URL || 'http://localhost:1337'}${attributes.image.data.attributes.url}`,
    description: attributes.description,
    specifications: attributes.specifications,
    features: attributes.features,
    availability: attributes.availability,
    rating: attributes.rating,
    reviews: attributes.reviews,
    featured: attributes.featured,
    price: attributes.price,
    currency: attributes.currency,
  };
};

export const transformStrapiBrand = (strapiBrand: StrapiBrand) => {
  const { attributes } = strapiBrand;
  
  return {
    id: strapiBrand.id.toString(),
    name: attributes.name,
    logo: `${process.env.REACT_APP_STRAPI_URL || 'http://localhost:1337'}${attributes.logo.data.attributes.url}`,
    description: attributes.description,
    country: attributes.country,
    founded: attributes.founded,
    specialties: attributes.specialties,
    website: attributes.website,
    productCount: attributes.productCount,
    featured: attributes.featured,
    markets: attributes.markets,
    certifications: attributes.certifications,
    // ... other fields
  };
};
```

### 4. Update Components to Use Strapi Data

Update `src/pages/ProductsPage.tsx`:

```typescript
import React, { useState, useEffect } from 'react';
import { strapiApi } from '../services/strapiApi';
import { transformStrapiProduct, transformStrapiBrand } from '../utils/strapiTransform';

const ProductsPage = () => {
  const [products, setProducts] = useState([]);
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productsData, brandsData] = await Promise.all([
          strapiApi.getProducts(),
          strapiApi.getBrands()
        ]);

        setProducts(productsData.map(transformStrapiProduct));
        setBrands(brandsData.map(transformStrapiBrand));
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  // Rest of component...
};
```

## Environment Variables

Add to your `.env` file:

```
REACT_APP_STRAPI_URL=http://localhost:1337
```

For production:
```
REACT_APP_STRAPI_URL=https://your-strapi-domain.com
```

## Migration Steps

1. **Setup Strapi** following the steps above
2. **Import existing data** into Strapi using the admin panel or API
3. **Update components** to use Strapi API instead of static data
4. **Test thoroughly** to ensure all functionality works
5. **Deploy Strapi** to production server
6. **Update environment variables** for production

## Benefits of Strapi Integration

- **Dynamic Content**: Easy content updates without code changes
- **Admin Interface**: User-friendly admin panel for content management
- **API-First**: RESTful and GraphQL APIs out of the box
- **Media Management**: Built-in media library for images
- **User Management**: Role-based access control
- **Scalability**: Easy to scale and extend

## Next Steps

1. Set up Strapi instance
2. Create the content types as described
3. Import existing product and brand data
4. Update frontend components to use Strapi API
5. Test and deploy

This integration will provide a much more flexible and maintainable content management system for the Nile Pro MEP website.
