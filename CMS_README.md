# Nile Pro MEP - Content Management System (CMS)

## Overview

This CMS system provides a comprehensive content management solution for the Nile Pro MEP website. It allows administrators to manage all website content including services, solutions, projects, team members, testimonials, company information, and site settings.

## Features

### Content Types Managed
- **Services** - MEP services with features, specifications, and categories
- **Solutions** - Industry-specific solutions with case studies and statistics
- **Projects** - Portfolio projects with detailed information and testimonials
- **Team Members** - Staff profiles with contact information and specialties
- **Testimonials** - Client testimonials with ratings and project details
- **Company Information** - About section, history, achievements, and certifications
- **Site Settings** - Contact information, social media, and general site configuration

### Key Features
- **Authentication System** - Secure login for admin access
- **CRUD Operations** - Create, Read, Update, Delete for all content types
- **Search & Filtering** - Advanced search and filtering capabilities
- **Responsive Design** - Mobile-friendly admin interface
- **Data Validation** - Form validation and error handling
- **Real-time Updates** - Changes reflect immediately on the website

## Access

### CMS Admin Panel
- **URL**: `/cms-admin`
- **Demo Credentials**:
  - Username: `admin`
  - Password: `admin123`

### Quick Access
- CMS Admin link is available in the website footer
- Direct URL: `https://your-domain.com/cms-admin`

## File Structure

```
src/
├── data/cms/                    # CMS data files
│   ├── services.ts             # Services data
│   ├── solutions.ts            # Solutions data
│   ├── projects.ts             # Projects and testimonials data
│   ├── team.ts                 # Team members data
│   ├── company.ts              # Company information
│   ├── settings.ts             # Site settings
│   └── index.ts                # Data exports
├── types/cms.ts                # TypeScript interfaces
├── utils/cmsUtils.ts           # CMS utility functions
├── components/cms/             # CMS components
│   ├── CMSAuth.tsx            # Authentication component
│   └── ServiceForm.tsx        # Service form component
└── pages/CMSAdmin.tsx          # Main admin interface
```

## Data Models

### Service
```typescript
interface Service {
  id: string;
  title: string;
  slug: string;
  shortDescription: string;
  fullDescription: string;
  icon: string;
  features: string[];
  badge?: string;
  complexity: 'Low' | 'Medium' | 'High';
  duration: string;
  rating: number;
  images: MediaItem[];
  category: ServiceCategory;
  tags: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Solution
```typescript
interface Solution {
  id: string;
  title: string;
  slug: string;
  shortDescription: string;
  fullDescription: string;
  detailedDescription: string;
  icon: string;
  features: string[];
  projects: string;
  category: SolutionCategory;
  services: SolutionService[];
  caseStudies: CaseStudy[];
  stats: Statistic[];
  images: MediaItem[];
  tags: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Project
```typescript
interface Project {
  id: string;
  title: string;
  slug: string;
  location: string;
  category: ProjectCategory;
  year: string;
  description: string;
  detailedDescription: string;
  scope: string;
  value: string;
  duration: string;
  client: string;
  image: string;
  gallery: string[];
  features: string[];
  challenges: string[];
  solutions: string[];
  results: string[];
  testimonial?: Testimonial;
  tags: string[];
  status: 'completed' | 'ongoing' | 'planned';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

## API Functions

### Services
- `getServices(filters?)` - Get all services with optional filtering
- `getServiceBySlug(slug)` - Get service by slug
- `getServiceCategories()` - Get all service categories

### Solutions
- `getSolutions(filters?)` - Get all solutions with optional filtering
- `getSolutionBySlug(slug)` - Get solution by slug
- `getSolutionCategories()` - Get all solution categories

### Projects
- `getProjects(filters?)` - Get all projects with optional filtering
- `getProjectBySlug(slug)` - Get project by slug
- `getProjectCategories()` - Get all project categories

### Team
- `getTeamMembers(filters?)` - Get all team members with optional filtering
- `getTeamMemberById(id)` - Get team member by ID
- `getDepartments()` - Get all departments

### Testimonials
- `getTestimonials(filters?)` - Get all testimonials with optional filtering

### Company & Settings
- `getCompanyInfo()` - Get company information
- `getSiteSettings()` - Get site settings
- `getSiteConfig()` - Get site configuration

### Search
- `searchContent(query, types?)` - Search across all content types

## Usage Examples

### Getting Services
```typescript
import { getServices } from '@/utils/cmsUtils';

// Get all active services
const servicesResponse = getServices({ isActive: true });
const services = servicesResponse.data;

// Get services by category
const electricalServices = getServices({ category: 'electrical' });

// Search services
const searchResults = getServices({ search: 'HVAC' });
```

### Using in Components
```typescript
import React from 'react';
import { getServices } from '@/utils/cmsUtils';

const ServicesComponent = () => {
  const servicesResponse = getServices({ isActive: true });
  const services = servicesResponse.data || [];

  return (
    <div>
      {services.map(service => (
        <div key={service.id}>
          <h3>{service.title}</h3>
          <p>{service.shortDescription}</p>
        </div>
      ))}
    </div>
  );
};
```

## Integration with Existing Components

The CMS is integrated with existing website components:

### Services Component
- Uses `getServices()` to fetch service data
- Falls back to hardcoded data if CMS data is unavailable
- Supports icon mapping for different service types

### Future Integrations
- Solutions component integration
- Projects/References component integration
- Team component integration
- Company information integration

## Security

### Authentication
- Simple username/password authentication for demo
- Session-based authentication (in-memory)
- Logout functionality

### Access Control
- Admin-only access to CMS interface
- Protected routes for CMS functionality

## Development

### Adding New Content Types
1. Define TypeScript interface in `src/types/cms.ts`
2. Create data file in `src/data/cms/`
3. Add utility functions in `src/utils/cmsUtils.ts`
4. Create form component in `src/components/cms/`
5. Add to admin interface in `src/pages/CMSAdmin.tsx`

### Extending Functionality
- Add image upload capabilities
- Implement rich text editor
- Add bulk operations
- Implement data export/import
- Add audit logging
- Implement role-based permissions

## Future Enhancements

### Planned Features
- **Image Management** - Upload and manage images
- **Rich Text Editor** - WYSIWYG editor for content
- **Bulk Operations** - Bulk edit, delete, and import
- **Data Export** - Export content to JSON/CSV
- **Audit Trail** - Track content changes
- **User Management** - Multiple users with different roles
- **API Integration** - Connect to external CMS or database
- **Backup & Restore** - Content backup and restoration
- **SEO Tools** - SEO optimization tools
- **Analytics** - Content performance analytics

### Technical Improvements
- Database integration (PostgreSQL, MongoDB)
- File upload to cloud storage (AWS S3, Cloudinary)
- Real-time collaboration
- Version control for content
- Automated testing
- Performance optimization

## Support

For technical support or questions about the CMS system:
- Contact: CodeSafir Development Team
- Email: <EMAIL>
- Website: https://www.codesafir.com

## License

This CMS system is part of the Nile Pro MEP website project and is proprietary software developed by CodeSafir.
