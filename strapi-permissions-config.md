# Strapi Authentication & Permissions Configuration

## Overview
This guide covers setting up authentication, user roles, and permissions for the Nile Pro MEP Strapi CMS.

## Default Roles

### Super Admin
- **Description**: Full system access
- **Permissions**: All CRUD operations on all content types
- **Users**: System administrators only

### Editor
- **Description**: Content management access
- **Permissions**: 
  - Create, Read, Update content
  - Cannot delete content
  - Cannot access system settings
- **Users**: Content managers, marketing team

### Author
- **Description**: Limited content creation
- **Permissions**:
  - Create and edit own content
  - Read all published content
  - Cannot delete or publish content
- **Users**: Content contributors

### Public
- **Description**: Frontend API access
- **Permissions**:
  - Read published content only
  - No write access
- **Users**: Website visitors (anonymous)

## API Permissions Configuration

### Public Role Permissions
Configure these permissions for the Public role to allow frontend access:

#### Services
- `find` ✅ (GET /api/services)
- `findOne` ✅ (GET /api/services/:id)
- `create` ❌
- `update` ❌
- `delete` ❌

#### Solutions
- `find` ✅ (GET /api/solutions)
- `findOne` ✅ (GET /api/solutions/:id)
- `create` ❌
- `update` ❌
- `delete` ❌

#### Projects
- `find` ✅ (GET /api/projects)
- `findOne` ✅ (GET /api/projects/:id)
- `create` ❌
- `update` ❌
- `delete` ❌

#### Team Members
- `find` ✅ (GET /api/team-members)
- `findOne` ✅ (GET /api/team-members/:id)
- `create` ❌
- `update` ❌
- `delete` ❌

#### Testimonials
- `find` ✅ (GET /api/testimonials)
- `findOne` ✅ (GET /api/testimonials/:id)
- `create` ❌
- `update` ❌
- `delete` ❌

#### Company Info
- `find` ✅ (GET /api/company-info)
- `create` ❌
- `update` ❌
- `delete` ❌

#### Site Settings
- `find` ✅ (GET /api/site-settings)
- `create` ❌
- `update` ❌
- `delete` ❌

### Authenticated Role Permissions
For logged-in users (if you implement user authentication):

#### All Content Types
- `find` ✅
- `findOne` ✅
- `create` ✅ (own content only)
- `update` ✅ (own content only)
- `delete` ❌

## Setting Up Permissions

### Step 1: Access Strapi Admin
1. Go to `http://localhost:1337/admin`
2. Log in with your admin credentials
3. Navigate to Settings > Users & Permissions Plugin > Roles

### Step 2: Configure Public Role
1. Click on "Public" role
2. Expand each content type section
3. Check the appropriate permissions as listed above
4. Click "Save"

### Step 3: Configure Authenticated Role (Optional)
1. Click on "Authenticated" role
2. Configure permissions based on your needs
3. Click "Save"

### Step 4: Create Custom Roles (Optional)
1. Click "Add new role"
2. Enter role details:
   - Name: "Editor"
   - Description: "Content management access"
3. Configure permissions
4. Click "Save"

## API Token Configuration

### Create API Token for Frontend
1. Go to Settings > API Tokens
2. Click "Create new API Token"
3. Configure:
   - **Name**: "Frontend API Token"
   - **Description**: "Token for frontend website access"
   - **Token duration**: "Unlimited"
   - **Token type**: "Read-only" or "Custom"
4. If Custom, select permissions:
   - Services: `find`, `findOne`
   - Solutions: `find`, `findOne`
   - Projects: `find`, `findOne`
   - Team Members: `find`, `findOne`
   - Testimonials: `find`, `findOne`
   - Company Info: `find`
   - Site Settings: `find`
5. Click "Save"
6. Copy the generated token for your `.env` file

### Create API Token for Migration
1. Create another token for data migration
2. Configure:
   - **Name**: "Migration Token"
   - **Description**: "Token for data migration script"
   - **Token duration**: "7 days" (temporary)
   - **Token type**: "Full access"
3. Click "Save"
4. Copy token for migration script

## Environment Variables

Add these to your frontend `.env` file:

```env
# Strapi Configuration
REACT_APP_USE_STRAPI=true
REACT_APP_STRAPI_URL=http://localhost:1337
REACT_APP_STRAPI_API_TOKEN=your-frontend-api-token-here
REACT_APP_STRAPI_FALLBACK=true
```

For migration script:
```bash
export STRAPI_URL=http://localhost:1337
export STRAPI_API_TOKEN=your-migration-token-here
```

## Security Best Practices

### Production Configuration
1. **Use HTTPS**: Always use HTTPS in production
2. **Restrict CORS**: Configure CORS to only allow your domain
3. **API Rate Limiting**: Implement rate limiting for API endpoints
4. **Token Rotation**: Regularly rotate API tokens
5. **Environment Variables**: Never commit tokens to version control

### CORS Configuration
Update `config/middlewares.js`:

```javascript
{
  name: 'strapi::cors',
  config: {
    enabled: true,
    headers: '*',
    origin: [
      'http://localhost:5173', // Development
      'https://your-domain.com', // Production
    ]
  }
}
```

### Rate Limiting
Install and configure rate limiting:

```bash
npm install @strapi/plugin-rate-limit
```

## User Management

### Creating Admin Users
1. Go to Settings > Administration Panel > Users
2. Click "Invite user"
3. Enter email and select role
4. User will receive invitation email

### Managing User Roles
1. Go to Settings > Users & Permissions Plugin > Users
2. Click on user to edit
3. Change role as needed
4. Click "Save"

## Content Workflow

### Draft and Publish
Strapi supports draft/publish workflow:
1. Content is created as draft by default
2. Only published content is available via API
3. Use `publishedAt` field to control visibility

### Content Moderation
Set up approval workflow:
1. Authors create content (draft)
2. Editors review and publish
3. Admins can unpublish if needed

## Monitoring and Logging

### API Usage Monitoring
Monitor API usage through:
1. Strapi admin panel logs
2. Server logs
3. Third-party monitoring tools

### Security Monitoring
Monitor for:
1. Failed authentication attempts
2. Unusual API usage patterns
3. Unauthorized access attempts

## Troubleshooting

### Common Permission Issues
1. **403 Forbidden**: Check role permissions
2. **401 Unauthorized**: Verify API token
3. **CORS Error**: Check CORS configuration
4. **Rate Limited**: Check rate limiting settings

### Debug Mode
Enable debug mode for troubleshooting:
```env
NODE_ENV=development
STRAPI_LOG_LEVEL=debug
```

This configuration ensures secure, scalable access to your Strapi CMS while maintaining proper content management workflows.
