import React from 'react';
import { useParams, Navigate } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ExternalLink,
  Building2,
  Globe,
  Calendar,
  Star,
  Eye,
  Heart,
  Download,
  MapPin,
  Phone,
  Mail
} from 'lucide-react';

const BrandPage = () => {
  const { brandId } = useParams();

  // Brand data (same as in ProductsPage)
  const brands = [
    {
      id: 'hiref',
      name: 'HiR<PERSON>',
      logo: '/brands/hiref-logo.png',
      description: 'Leading manufacturer of commercial refrigeration and HVAC equipment',
      country: 'Italy',
      founded: '1963',
      specialties: ['Commercial Refrigeration', 'HVAC Systems', 'Heat Pumps'],
      website: 'https://www.hiref.it',
      productCount: 5,
      featured: true,
      fullDescription: 'HiRef is an Italian company that has been at the forefront of commercial refrigeration and HVAC technology since 1963. With over 60 years of experience, HiRef has established itself as a trusted partner for businesses worldwide, providing innovative and energy-efficient solutions for commercial and industrial applications.',
      headquarters: 'Tribano, Italy',
      employees: '500+',
      markets: ['Europe', 'Middle East', 'Africa', 'Asia'],
      certifications: ['ISO 9001', 'ISO 14001', 'CE Marking'],
      contact: {
        phone: '+39 049 9588611',
        email: '<EMAIL>',
        address: 'Via Sartor, 2 - 35020 Tribano (PD), Italy'
      }
    },
    {
      id: 'dkc',
      name: 'DKC',
      logo: '/brands/dkc-logo.png',
      description: 'Premium electrical enclosures and cable management solutions',
      country: 'Russia',
      founded: '1998',
      specialties: ['Electrical Enclosures', 'Cable Management', 'Industrial Solutions'],
      website: 'https://www.dkc.ru',
      productCount: 3,
      featured: true,
      fullDescription: 'DKC is a leading Russian manufacturer of electrical equipment and cable management systems. Since 1998, the company has been providing high-quality solutions for electrical installations, industrial automation, and infrastructure projects across various industries.',
      headquarters: 'Moscow, Russia',
      employees: '1000+',
      markets: ['Russia', 'CIS', 'Europe', 'Middle East'],
      certifications: ['GOST R', 'EAC', 'ISO 9001'],
      contact: {
        phone: '****** 221-21-21',
        email: '<EMAIL>',
        address: 'Moscow, Russia'
      }
    },
    {
      id: 'carrier',
      name: 'Carrier',
      logo: '/brands/carrier-logo.png',
      description: 'Global leader in heating, ventilating and air conditioning systems',
      country: 'USA',
      founded: '1915',
      specialties: ['Air Conditioning', 'Heating Systems', 'Refrigeration'],
      website: 'https://www.carrier.com',
      productCount: 4,
      featured: true,
      fullDescription: 'Carrier is a world leader in heating, air-conditioning and refrigeration solutions. Founded in 1915 by Willis Carrier, the inventor of modern air conditioning, the company has been at the forefront of HVAC innovation for over a century, serving residential, commercial, and industrial markets worldwide.',
      headquarters: 'Palm Beach Gardens, Florida, USA',
      employees: '53,000+',
      markets: ['Global'],
      certifications: ['AHRI', 'UL', 'ENERGY STAR'],
      contact: {
        phone: '******-227-7437',
        email: '<EMAIL>',
        address: 'Palm Beach Gardens, FL, USA'
      }
    },
    {
      id: 'daikin',
      name: 'Daikin',
      logo: '/brands/daikin-logo.png',
      description: 'World leader in air conditioning and refrigeration technology',
      country: 'Japan',
      founded: '1924',
      specialties: ['Air Conditioning', 'Heat Pumps', 'Refrigeration'],
      website: 'https://www.daikin.com',
      productCount: 2,
      featured: true,
      fullDescription: 'Daikin Industries is a Japanese multinational air conditioning manufacturing company headquartered in Osaka, Japan. Founded in 1924, Daikin is the world\'s largest air conditioning manufacturer and has been pioneering HVAC technology for nearly a century.',
      headquarters: 'Osaka, Japan',
      employees: '88,000+',
      markets: ['Global'],
      certifications: ['ISO 9001', 'ISO 14001', 'ENERGY STAR'],
      contact: {
        phone: '+81-6-6373-4312',
        email: '<EMAIL>',
        address: 'Osaka, Japan'
      }
    },
    {
      id: 'trane',
      name: 'Trane',
      logo: '/brands/trane-logo.png',
      description: 'Innovative climate control solutions for commercial and residential',
      country: 'USA',
      founded: '1885',
      specialties: ['Climate Control', 'Energy Efficiency', 'Smart Systems'],
      website: 'https://www.trane.com',
      productCount: 2,
      featured: false,
      fullDescription: 'Trane is a world leader in air conditioning systems, services and solutions. Founded in 1885, Trane has been providing innovative climate control solutions for residential and commercial applications, focusing on energy efficiency and sustainability.',
      headquarters: 'Davidson, North Carolina, USA',
      employees: '29,000+',
      markets: ['Global'],
      certifications: ['AHRI', 'ENERGY STAR', 'LEED'],
      contact: {
        phone: '******-482-7263',
        email: '<EMAIL>',
        address: 'Davidson, NC, USA'
      }
    },
    {
      id: 'york',
      name: 'York',
      logo: '/brands/york-logo.png',
      description: 'Comprehensive HVAC solutions for every application',
      country: 'USA',
      founded: '1874',
      specialties: ['HVAC Systems', 'Chillers', 'Air Handlers'],
      website: 'https://www.york.com',
      productCount: 2,
      featured: false,
      fullDescription: 'York is a leading global supplier of heating, ventilation, air conditioning and refrigeration equipment and services. Founded in 1874, York has been providing reliable HVAC solutions for commercial, industrial, and residential applications for over 140 years.',
      headquarters: 'Milwaukee, Wisconsin, USA',
      employees: '15,000+',
      markets: ['Global'],
      certifications: ['AHRI', 'UL', 'ENERGY STAR'],
      contact: {
        phone: '******-861-1001',
        email: '<EMAIL>',
        address: 'Milwaukee, WI, USA'
      }
    }
  ];

  const brand = brands.find(b => b.id === brandId);

  if (!brand) {
    return <Navigate to="/products" replace />;
  }

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="w-32 h-32 mx-auto mb-8 bg-white rounded-2xl flex items-center justify-center shadow-lg">
              <img
                src={brand.logo}
                alt={`${brand.name} Logo`}
                className="max-w-24 max-h-24 object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder.svg';
                }}
              />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              {brand.name}
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              {brand.description}
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground mb-8">
              <span className="flex items-center">
                <Globe className="h-4 w-4 mr-2" />
                {brand.country}
              </span>
              <span className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Founded {brand.founded}
              </span>
              <span className="flex items-center">
                <Building2 className="h-4 w-4 mr-2" />
                {brand.employees} Employees
              </span>
            </div>
            <div className="flex gap-4 justify-center">
              <Button 
                onClick={() => window.open(brand.website, '_blank')}
                className="bg-gradient-primary hover:opacity-90"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Visit Website
              </Button>
              <Button variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                View Products ({brand.productCount})
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Information */}
      <section className="py-16 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* About */}
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-6">About {brand.name}</h2>
              <p className="text-muted-foreground text-lg leading-relaxed mb-8">
                {brand.fullDescription}
              </p>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-3">Specialties</h3>
                  <div className="flex flex-wrap gap-2">
                    {brand.specialties.map((specialty, idx) => (
                      <Badge key={idx} variant="secondary" className="text-sm">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-3">Markets</h3>
                  <div className="flex flex-wrap gap-2">
                    {brand.markets.map((market, idx) => (
                      <Badge key={idx} variant="outline" className="text-sm">
                        {market}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-3">Certifications</h3>
                  <div className="flex flex-wrap gap-2">
                    {brand.certifications.map((cert, idx) => (
                      <Badge key={idx} variant="default" className="text-sm">
                        {cert}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-6">Contact Information</h2>
              
              <div className="space-y-6">
                <Card className="border-border">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-primary/10 rounded-lg">
                        <MapPin className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground mb-1">Headquarters</h3>
                        <p className="text-muted-foreground">{brand.headquarters}</p>
                        <p className="text-sm text-muted-foreground mt-1">{brand.contact.address}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-primary/10 rounded-lg">
                        <Phone className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground mb-1">Phone</h3>
                        <p className="text-muted-foreground">{brand.contact.phone}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-primary/10 rounded-lg">
                        <Mail className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground mb-1">Email</h3>
                        <p className="text-muted-foreground">{brand.contact.email}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default BrandPage;
