import { 
  Service, 
  Solution, 
  Project, 
  TeamMember, 
  Testimonial, 
  CompanyInfo, 
  SiteSettings,
  CMSResponse,
  CMSListResponse,
  BaseEntity
} from '@/types/cms';

// Import data
import { services, serviceCategories } from '@/data/cms/services';
import { solutions, solutionCategories } from '@/data/cms/solutions';
import { projects, testimonials, projectCategories } from '@/data/cms/projects';
import { teamMembers, departments } from '@/data/cms/team';
import { companyInfo } from '@/data/cms/company';
import { siteSettings, siteConfig } from '@/data/cms/settings';

// Generic utility functions
export const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

export const formatDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const createBaseEntity = (id?: string): Omit<BaseEntity, 'id'> => {
  const now = new Date().toISOString();
  return {
    createdAt: now,
    updatedAt: now,
    isActive: true
  };
};

// Services API
export const getServices = (filters?: {
  category?: string;
  search?: string;
  isActive?: boolean;
}): CMSListResponse<Service> => {
  try {
    let filteredServices = [...services];

    if (filters?.isActive !== undefined) {
      filteredServices = filteredServices.filter(service => service.isActive === filters.isActive);
    }

    if (filters?.category) {
      filteredServices = filteredServices.filter(service => service.category === filters.category);
    }

    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      filteredServices = filteredServices.filter(service =>
        service.title.toLowerCase().includes(searchLower) ||
        service.shortDescription.toLowerCase().includes(searchLower) ||
        service.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return {
      success: true,
      data: filteredServices,
      pagination: {
        page: 1,
        limit: filteredServices.length,
        total: filteredServices.length,
        totalPages: 1
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch services',
      data: []
    };
  }
};

export const getServiceBySlug = (slug: string): CMSResponse<Service> => {
  try {
    const service = services.find(s => s.slug === slug && s.isActive);
    if (!service) {
      return {
        success: false,
        error: 'Service not found'
      };
    }
    return {
      success: true,
      data: service
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch service'
    };
  }
};

export const getServiceCategories = () => serviceCategories;

// Solutions API
export const getSolutions = (filters?: {
  category?: string;
  search?: string;
  isActive?: boolean;
}): CMSListResponse<Solution> => {
  try {
    let filteredSolutions = [...solutions];

    if (filters?.isActive !== undefined) {
      filteredSolutions = filteredSolutions.filter(solution => solution.isActive === filters.isActive);
    }

    if (filters?.category) {
      filteredSolutions = filteredSolutions.filter(solution => solution.category === filters.category);
    }

    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      filteredSolutions = filteredSolutions.filter(solution =>
        solution.title.toLowerCase().includes(searchLower) ||
        solution.shortDescription.toLowerCase().includes(searchLower) ||
        solution.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return {
      success: true,
      data: filteredSolutions,
      pagination: {
        page: 1,
        limit: filteredSolutions.length,
        total: filteredSolutions.length,
        totalPages: 1
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch solutions',
      data: []
    };
  }
};

export const getSolutionBySlug = (slug: string): CMSResponse<Solution> => {
  try {
    const solution = solutions.find(s => s.slug === slug && s.isActive);
    if (!solution) {
      return {
        success: false,
        error: 'Solution not found'
      };
    }
    return {
      success: true,
      data: solution
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch solution'
    };
  }
};

export const getSolutionCategories = () => solutionCategories;

// Projects API
export const getProjects = (filters?: {
  category?: string;
  status?: string;
  year?: string;
  search?: string;
  isActive?: boolean;
}): CMSListResponse<Project> => {
  try {
    let filteredProjects = [...projects];

    if (filters?.isActive !== undefined) {
      filteredProjects = filteredProjects.filter(project => project.isActive === filters.isActive);
    }

    if (filters?.category) {
      filteredProjects = filteredProjects.filter(project => project.category === filters.category);
    }

    if (filters?.status) {
      filteredProjects = filteredProjects.filter(project => project.status === filters.status);
    }

    if (filters?.year) {
      filteredProjects = filteredProjects.filter(project => project.year === filters.year);
    }

    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      filteredProjects = filteredProjects.filter(project =>
        project.title.toLowerCase().includes(searchLower) ||
        project.description.toLowerCase().includes(searchLower) ||
        project.client.toLowerCase().includes(searchLower) ||
        project.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return {
      success: true,
      data: filteredProjects,
      pagination: {
        page: 1,
        limit: filteredProjects.length,
        total: filteredProjects.length,
        totalPages: 1
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch projects',
      data: []
    };
  }
};

export const getProjectBySlug = (slug: string): CMSResponse<Project> => {
  try {
    const project = projects.find(p => p.slug === slug && p.isActive);
    if (!project) {
      return {
        success: false,
        error: 'Project not found'
      };
    }
    return {
      success: true,
      data: project
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch project'
    };
  }
};

export const getProjectCategories = () => projectCategories;

// Testimonials API
export const getTestimonials = (filters?: {
  category?: string;
  rating?: number;
  isActive?: boolean;
}): CMSListResponse<Testimonial> => {
  try {
    let filteredTestimonials = [...testimonials];

    if (filters?.isActive !== undefined) {
      filteredTestimonials = filteredTestimonials.filter(testimonial => testimonial.isActive === filters.isActive);
    }

    if (filters?.category) {
      filteredTestimonials = filteredTestimonials.filter(testimonial => testimonial.category === filters.category);
    }

    if (filters?.rating) {
      filteredTestimonials = filteredTestimonials.filter(testimonial => testimonial.rating >= filters.rating);
    }

    return {
      success: true,
      data: filteredTestimonials,
      pagination: {
        page: 1,
        limit: filteredTestimonials.length,
        total: filteredTestimonials.length,
        totalPages: 1
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch testimonials',
      data: []
    };
  }
};

// Team API
export const getTeamMembers = (filters?: {
  department?: string;
  isActive?: boolean;
}): CMSListResponse<TeamMember> => {
  try {
    let filteredMembers = [...teamMembers];

    if (filters?.isActive !== undefined) {
      filteredMembers = filteredMembers.filter(member => member.isActive === filters.isActive);
    }

    if (filters?.department) {
      filteredMembers = filteredMembers.filter(member => member.department.toLowerCase() === filters.department.toLowerCase());
    }

    return {
      success: true,
      data: filteredMembers,
      pagination: {
        page: 1,
        limit: filteredMembers.length,
        total: filteredMembers.length,
        totalPages: 1
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch team members',
      data: []
    };
  }
};

export const getTeamMemberById = (id: string): CMSResponse<TeamMember> => {
  try {
    const member = teamMembers.find(m => m.id === id && m.isActive);
    if (!member) {
      return {
        success: false,
        error: 'Team member not found'
      };
    }
    return {
      success: true,
      data: member
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch team member'
    };
  }
};

export const getDepartments = () => departments;

// Company Info API
export const getCompanyInfo = (): CMSResponse<CompanyInfo> => {
  try {
    return {
      success: true,
      data: companyInfo
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch company information'
    };
  }
};

// Site Settings API
export const getSiteSettings = (): CMSResponse<SiteSettings> => {
  try {
    return {
      success: true,
      data: siteSettings
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch site settings'
    };
  }
};

export const getSiteConfig = () => siteConfig;

// Search API
export const searchContent = (query: string, types?: string[]): CMSResponse<any[]> => {
  try {
    const results: any[] = [];
    const searchLower = query.toLowerCase();

    // Search services
    if (!types || types.includes('services')) {
      const serviceResults = services.filter(service =>
        service.isActive && (
          service.title.toLowerCase().includes(searchLower) ||
          service.shortDescription.toLowerCase().includes(searchLower) ||
          service.tags.some(tag => tag.toLowerCase().includes(searchLower))
        )
      ).map(service => ({ ...service, type: 'service' }));
      results.push(...serviceResults);
    }

    // Search solutions
    if (!types || types.includes('solutions')) {
      const solutionResults = solutions.filter(solution =>
        solution.isActive && (
          solution.title.toLowerCase().includes(searchLower) ||
          solution.shortDescription.toLowerCase().includes(searchLower) ||
          solution.tags.some(tag => tag.toLowerCase().includes(searchLower))
        )
      ).map(solution => ({ ...solution, type: 'solution' }));
      results.push(...solutionResults);
    }

    // Search projects
    if (!types || types.includes('projects')) {
      const projectResults = projects.filter(project =>
        project.isActive && (
          project.title.toLowerCase().includes(searchLower) ||
          project.description.toLowerCase().includes(searchLower) ||
          project.client.toLowerCase().includes(searchLower) ||
          project.tags.some(tag => tag.toLowerCase().includes(searchLower))
        )
      ).map(project => ({ ...project, type: 'project' }));
      results.push(...projectResults);
    }

    return {
      success: true,
      data: results
    };
  } catch (error) {
    return {
      success: false,
      error: 'Search failed',
      data: []
    };
  }
};
