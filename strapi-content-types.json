{"service": {"kind": "collectionType", "collectionName": "services", "info": {"singularName": "service", "pluralName": "services", "displayName": "Service", "description": "MEP Services offered by Nile Pro"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "title", "required": true}, "shortDescription": {"type": "text", "required": true, "maxLength": 500}, "fullDescription": {"type": "richtext", "required": true}, "icon": {"type": "string", "default": "Zap", "maxLength": 50}, "features": {"type": "json", "required": false}, "badge": {"type": "string", "maxLength": 100}, "complexity": {"type": "enumeration", "enum": ["Low", "Medium", "High"], "default": "Medium", "required": true}, "duration": {"type": "string", "maxLength": 100}, "rating": {"type": "decimal", "min": 1, "max": 5, "default": 4.5}, "category": {"type": "enumeration", "enum": ["electrical", "mechanical", "plumbing", "hvac", "automation", "maintenance"], "required": true}, "tags": {"type": "json", "required": false}, "images": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}}}, "solution": {"kind": "collectionType", "collectionName": "solutions", "info": {"singularName": "solution", "pluralName": "solutions", "displayName": "Solution", "description": "Industry-specific MEP solutions"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "title", "required": true}, "shortDescription": {"type": "text", "required": true, "maxLength": 500}, "fullDescription": {"type": "richtext", "required": true}, "detailedDescription": {"type": "richtext"}, "icon": {"type": "string", "default": "Building2", "maxLength": 50}, "features": {"type": "json", "required": false}, "projects": {"type": "string", "maxLength": 100}, "category": {"type": "enumeration", "enum": ["hospitality", "healthcare", "industrial", "office", "food-beverage", "commercial"], "required": true}, "services": {"type": "json", "required": false}, "caseStudies": {"type": "json", "required": false}, "stats": {"type": "json", "required": false}, "tags": {"type": "json", "required": false}, "images": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}}}, "project": {"kind": "collectionType", "collectionName": "projects", "info": {"singularName": "project", "pluralName": "projects", "displayName": "Project", "description": "Portfolio projects and case studies"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "title", "required": true}, "location": {"type": "string", "required": true, "maxLength": 255}, "category": {"type": "enumeration", "enum": ["hospitality", "healthcare", "industrial", "commercial", "residential", "infrastructure"], "required": true}, "year": {"type": "string", "required": true, "maxLength": 4}, "description": {"type": "text", "required": true, "maxLength": 1000}, "detailedDescription": {"type": "richtext"}, "scope": {"type": "string", "maxLength": 255}, "value": {"type": "string", "maxLength": 100}, "duration": {"type": "string", "maxLength": 100}, "client": {"type": "string", "required": true, "maxLength": 255}, "features": {"type": "json", "required": false}, "challenges": {"type": "json", "required": false}, "solutions": {"type": "json", "required": false}, "results": {"type": "json", "required": false}, "tags": {"type": "json", "required": false}, "status": {"type": "enumeration", "enum": ["completed", "ongoing", "planned"], "default": "completed", "required": true}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "gallery": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "testimonial": {"type": "relation", "relation": "oneToOne", "target": "api::testimonial.testimonial"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}}}, "team-member": {"kind": "collectionType", "collectionName": "team_members", "info": {"singularName": "team-member", "pluralName": "team-members", "displayName": "Team Member", "description": "Company team members and staff"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255}, "position": {"type": "string", "required": true, "maxLength": 255}, "department": {"type": "string", "required": true, "maxLength": 100}, "email": {"type": "email", "required": true}, "phone": {"type": "string", "maxLength": 50}, "bio": {"type": "text", "maxLength": 1000}, "linkedin": {"type": "string", "maxLength": 500}, "specialties": {"type": "json", "required": false}, "experience": {"type": "string", "maxLength": 100}, "education": {"type": "json", "required": false}, "certifications": {"type": "json", "required": false}, "languages": {"type": "json", "required": false}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}}}, "testimonial": {"kind": "collectionType", "collectionName": "testimonials", "info": {"singularName": "testimonial", "pluralName": "testimonials", "displayName": "Testimonial", "description": "Client testimonials and reviews"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255}, "position": {"type": "string", "required": true, "maxLength": 255}, "company": {"type": "string", "required": true, "maxLength": 255}, "quote": {"type": "text", "required": true, "maxLength": 1000}, "rating": {"type": "integer", "min": 1, "max": 5, "default": 5, "required": true}, "projectValue": {"type": "string", "maxLength": 100}, "completionYear": {"type": "string", "required": true, "maxLength": 4}, "category": {"type": "enumeration", "enum": ["client", "partner", "employee", "vendor"], "default": "client", "required": true}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "project": {"type": "relation", "relation": "oneToOne", "target": "api::project.project", "inversedBy": "testimonial"}}}, "company-info": {"kind": "singleType", "collectionName": "company_infos", "info": {"singularName": "company-info", "pluralName": "company-infos", "displayName": "Company Info", "description": "Company information and settings"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 255}, "tagline": {"type": "string", "maxLength": 500}, "description": {"type": "text", "required": true}, "foundedYear": {"type": "integer", "required": true}, "experience": {"type": "integer", "required": true}, "mission": {"type": "text", "required": true}, "vision": {"type": "text", "required": true}, "values": {"type": "json", "required": false}, "achievements": {"type": "json", "required": false}, "certifications": {"type": "json", "required": false}, "stats": {"type": "json", "required": false}, "history": {"type": "json", "required": false}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "leadership": {"type": "relation", "relation": "oneToMany", "target": "api::team-member.team-member"}}}, "site-settings": {"kind": "singleType", "collectionName": "site_settings", "info": {"singularName": "site-settings", "pluralName": "site-settings", "displayName": "Site Settings", "description": "Website configuration and settings"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"siteName": {"type": "string", "required": true, "maxLength": 255}, "siteDescription": {"type": "text", "required": true}, "siteKeywords": {"type": "json", "required": false}, "contactInfo": {"type": "json", "required": true}, "socialMedia": {"type": "json", "required": false}, "businessHours": {"type": "json", "required": false}, "seoSettings": {"type": "json", "required": false}, "maintenanceMode": {"type": "boolean", "default": false}, "analytics": {"type": "json", "required": false}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "favicon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}}}}