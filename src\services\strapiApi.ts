import axios, { AxiosResponse } from 'axios';

// Strapi API Configuration
const STRAPI_URL = process.env.REACT_APP_STRAPI_URL || 'http://localhost:1337';
const API_TOKEN = process.env.REACT_APP_STRAPI_API_TOKEN;

// Create axios instance
const strapiApi = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add authentication token if available
if (API_TOKEN) {
  strapiApi.defaults.headers.Authorization = `Bearer ${API_TOKEN}`;
}

// Request interceptor for debugging
strapiApi.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to: ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
strapiApi.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Strapi API Response Types
export interface StrapiResponse<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface StrapiEntity {
  id: number;
  documentId: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale?: string;
}

// API Service Functions
export class StrapiApiService {
  // Generic CRUD operations
  static async find<T>(
    endpoint: string,
    params?: {
      populate?: string | string[];
      filters?: Record<string, any>;
      sort?: string | string[];
      pagination?: {
        page?: number;
        pageSize?: number;
        start?: number;
        limit?: number;
      };
      locale?: string;
    }
  ): Promise<StrapiResponse<T[]>> {
    const response: AxiosResponse<StrapiResponse<T[]>> = await strapiApi.get(endpoint, {
      params: {
        ...params,
        populate: Array.isArray(params?.populate) ? params.populate.join(',') : params?.populate,
        sort: Array.isArray(params?.sort) ? params.sort.join(',') : params?.sort,
      },
    });
    return response.data;
  }

  static async findOne<T>(
    endpoint: string,
    id: string | number,
    params?: {
      populate?: string | string[];
      locale?: string;
    }
  ): Promise<{ data: T }> {
    const response: AxiosResponse<{ data: T }> = await strapiApi.get(`${endpoint}/${id}`, {
      params: {
        ...params,
        populate: Array.isArray(params?.populate) ? params.populate.join(',') : params?.populate,
      },
    });
    return response.data;
  }

  static async create<T>(endpoint: string, data: Partial<T>): Promise<{ data: T }> {
    const response: AxiosResponse<{ data: T }> = await strapiApi.post(endpoint, { data });
    return response.data;
  }

  static async update<T>(
    endpoint: string,
    id: string | number,
    data: Partial<T>
  ): Promise<{ data: T }> {
    const response: AxiosResponse<{ data: T }> = await strapiApi.put(`${endpoint}/${id}`, { data });
    return response.data;
  }

  static async delete(endpoint: string, id: string | number): Promise<{ data: any }> {
    const response: AxiosResponse<{ data: any }> = await strapiApi.delete(`${endpoint}/${id}`);
    return response.data;
  }

  // Services API
  static async getServices(params?: {
    category?: string;
    search?: string;
    published?: boolean;
    page?: number;
    pageSize?: number;
  }) {
    const filters: Record<string, any> = {};
    
    if (params?.category) {
      filters.category = { $eq: params.category };
    }
    
    if (params?.search) {
      filters.$or = [
        { title: { $containsi: params.search } },
        { shortDescription: { $containsi: params.search } },
        { tags: { $containsi: params.search } },
      ];
    }

    if (params?.published !== undefined) {
      filters.publishedAt = params.published ? { $notNull: true } : { $null: true };
    }

    return this.find('services', {
      filters,
      populate: ['images'],
      pagination: {
        page: params?.page || 1,
        pageSize: params?.pageSize || 25,
      },
      sort: ['createdAt:desc'],
    });
  }

  static async getServiceBySlug(slug: string) {
    const response = await this.find('services', {
      filters: { slug: { $eq: slug } },
      populate: ['images'],
    });
    return response.data[0] || null;
  }

  // Solutions API
  static async getSolutions(params?: {
    category?: string;
    search?: string;
    published?: boolean;
    page?: number;
    pageSize?: number;
  }) {
    const filters: Record<string, any> = {};
    
    if (params?.category) {
      filters.category = { $eq: params.category };
    }
    
    if (params?.search) {
      filters.$or = [
        { title: { $containsi: params.search } },
        { shortDescription: { $containsi: params.search } },
        { tags: { $containsi: params.search } },
      ];
    }

    if (params?.published !== undefined) {
      filters.publishedAt = params.published ? { $notNull: true } : { $null: true };
    }

    return this.find('solutions', {
      filters,
      populate: ['images'],
      pagination: {
        page: params?.page || 1,
        pageSize: params?.pageSize || 25,
      },
      sort: ['createdAt:desc'],
    });
  }

  static async getSolutionBySlug(slug: string) {
    const response = await this.find('solutions', {
      filters: { slug: { $eq: slug } },
      populate: ['images'],
    });
    return response.data[0] || null;
  }

  // Projects API
  static async getProjects(params?: {
    category?: string;
    status?: string;
    year?: string;
    search?: string;
    published?: boolean;
    page?: number;
    pageSize?: number;
  }) {
    const filters: Record<string, any> = {};
    
    if (params?.category) {
      filters.category = { $eq: params.category };
    }
    
    if (params?.status) {
      filters.status = { $eq: params.status };
    }
    
    if (params?.year) {
      filters.year = { $eq: params.year };
    }
    
    if (params?.search) {
      filters.$or = [
        { title: { $containsi: params.search } },
        { description: { $containsi: params.search } },
        { client: { $containsi: params.search } },
        { tags: { $containsi: params.search } },
      ];
    }

    if (params?.published !== undefined) {
      filters.publishedAt = params.published ? { $notNull: true } : { $null: true };
    }

    return this.find('projects', {
      filters,
      populate: ['image', 'gallery', 'testimonial'],
      pagination: {
        page: params?.page || 1,
        pageSize: params?.pageSize || 25,
      },
      sort: ['year:desc', 'createdAt:desc'],
    });
  }

  static async getProjectBySlug(slug: string) {
    const response = await this.find('projects', {
      filters: { slug: { $eq: slug } },
      populate: ['image', 'gallery', 'testimonial'],
    });
    return response.data[0] || null;
  }

  // Team Members API
  static async getTeamMembers(params?: {
    department?: string;
    published?: boolean;
    page?: number;
    pageSize?: number;
  }) {
    const filters: Record<string, any> = {};
    
    if (params?.department) {
      filters.department = { $eq: params.department };
    }

    if (params?.published !== undefined) {
      filters.publishedAt = params.published ? { $notNull: true } : { $null: true };
    }

    return this.find('team-members', {
      filters,
      populate: ['image'],
      pagination: {
        page: params?.page || 1,
        pageSize: params?.pageSize || 50,
      },
      sort: ['position:asc', 'name:asc'],
    });
  }

  // Testimonials API
  static async getTestimonials(params?: {
    category?: string;
    rating?: number;
    published?: boolean;
    page?: number;
    pageSize?: number;
  }) {
    const filters: Record<string, any> = {};
    
    if (params?.category) {
      filters.category = { $eq: params.category };
    }
    
    if (params?.rating) {
      filters.rating = { $gte: params.rating };
    }

    if (params?.published !== undefined) {
      filters.publishedAt = params.published ? { $notNull: true } : { $null: true };
    }

    return this.find('testimonials', {
      filters,
      populate: ['image', 'project'],
      pagination: {
        page: params?.page || 1,
        pageSize: params?.pageSize || 25,
      },
      sort: ['rating:desc', 'createdAt:desc'],
    });
  }

  // Company Info API
  static async getCompanyInfo() {
    const response = await this.find('company-info', {
      populate: ['logo', 'leadership', 'leadership.image'],
    });
    return response.data[0] || null;
  }

  // Site Settings API
  static async getSiteSettings() {
    const response = await this.find('site-settings', {
      populate: ['logo', 'favicon'],
    });
    return response.data[0] || null;
  }

  // Search across all content types
  static async searchContent(query: string, contentTypes?: string[]) {
    const types = contentTypes || ['services', 'solutions', 'projects', 'team-members'];
    const results = [];

    for (const type of types) {
      try {
        const response = await this.find(type, {
          filters: {
            $or: [
              { title: { $containsi: query } },
              { name: { $containsi: query } },
              { description: { $containsi: query } },
              { shortDescription: { $containsi: query } },
            ],
          },
          populate: type === 'team-members' ? ['image'] : ['images', 'image'],
          pagination: { pageSize: 10 },
        });

        results.push(...response.data.map(item => ({ ...item, contentType: type })));
      } catch (error) {
        console.error(`Error searching ${type}:`, error);
      }
    }

    return results;
  }
}

export default strapiApi;
