import { StrapiApiService } from '@/services/strapiApi';
import { 
  Service, 
  Solution, 
  Project, 
  TeamMember, 
  Testimonial, 
  CompanyInfo, 
  SiteSettings,
  CMSResponse,
  CMSListResponse
} from '@/types/cms';

// Fallback to JSON data if Strapi is unavailable
import { 
  getServices as getJsonServices,
  getSolutions as getJsonSolutions,
  getProjects as getJsonProjects,
  getTeamMembers as getJsonTeamMembers,
  getTestimonials as getJsonTestimonials,
  getCompanyInfo as getJsonCompanyInfo,
  getSiteSettings as getJsonSiteSettings,
  searchContent as searchJsonContent
} from './cmsUtils';

// Configuration
const USE_STRAPI = process.env.REACT_APP_USE_STRAPI === 'true';
const STRAPI_FALLBACK = process.env.REACT_APP_STRAPI_FALLBACK !== 'false';

// Helper function to transform Strapi response to CMS format
const transformStrapiResponse = <T>(strapiData: any): T => {
  if (!strapiData) return strapiData;
  
  // Transform Strapi entity to CMS format
  const transformed = {
    id: strapiData.documentId || strapiData.id?.toString(),
    ...strapiData,
    createdAt: strapiData.createdAt,
    updatedAt: strapiData.updatedAt,
    isActive: !!strapiData.publishedAt,
  };

  // Transform media fields
  if (strapiData.images?.data) {
    transformed.images = strapiData.images.data.map((img: any) => ({
      url: img.url,
      alt: img.alternativeText || img.name,
      caption: img.caption,
      type: 'image'
    }));
  }

  if (strapiData.image?.data) {
    transformed.image = strapiData.image.data.url;
  }

  if (strapiData.gallery?.data) {
    transformed.gallery = strapiData.gallery.data.map((img: any) => img.url);
  }

  return transformed as T;
};

// Helper function to handle API calls with fallback
const withFallback = async <T>(
  strapiCall: () => Promise<any>,
  fallbackCall: () => CMSResponse<T> | CMSListResponse<T>
): Promise<CMSResponse<T> | CMSListResponse<T>> => {
  if (!USE_STRAPI) {
    return fallbackCall();
  }

  try {
    const strapiResponse = await strapiCall();
    
    if (Array.isArray(strapiResponse.data)) {
      // List response
      return {
        success: true,
        data: strapiResponse.data.map(transformStrapiResponse),
        pagination: strapiResponse.meta.pagination ? {
          page: strapiResponse.meta.pagination.page,
          limit: strapiResponse.meta.pagination.pageSize,
          total: strapiResponse.meta.pagination.total,
          totalPages: strapiResponse.meta.pagination.pageCount
        } : undefined
      } as CMSListResponse<T>;
    } else {
      // Single response
      return {
        success: true,
        data: transformStrapiResponse(strapiResponse.data)
      } as CMSResponse<T>;
    }
  } catch (error) {
    console.error('Strapi API error:', error);
    
    if (STRAPI_FALLBACK) {
      console.log('Falling back to JSON data');
      return fallbackCall();
    }
    
    return {
      success: false,
      error: 'Failed to fetch data from Strapi',
      data: Array.isArray((fallbackCall() as any).data) ? [] : undefined
    } as any;
  }
};

// Services API
export const getServices = async (filters?: {
  category?: string;
  search?: string;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}): Promise<CMSListResponse<Service>> => {
  return withFallback(
    () => StrapiApiService.getServices({
      category: filters?.category,
      search: filters?.search,
      published: filters?.isActive,
      page: filters?.page,
      pageSize: filters?.pageSize
    }),
    () => getJsonServices(filters)
  );
};

export const getServiceBySlug = async (slug: string): Promise<CMSResponse<Service>> => {
  return withFallback(
    async () => {
      const service = await StrapiApiService.getServiceBySlug(slug);
      return { data: service };
    },
    () => {
      const jsonResponse = getJsonServices({ search: slug });
      const service = jsonResponse.data?.find(s => s.slug === slug);
      return {
        success: !!service,
        data: service,
        error: service ? undefined : 'Service not found'
      };
    }
  );
};

// Solutions API
export const getSolutions = async (filters?: {
  category?: string;
  search?: string;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}): Promise<CMSListResponse<Solution>> => {
  return withFallback(
    () => StrapiApiService.getSolutions({
      category: filters?.category,
      search: filters?.search,
      published: filters?.isActive,
      page: filters?.page,
      pageSize: filters?.pageSize
    }),
    () => getJsonSolutions(filters)
  );
};

export const getSolutionBySlug = async (slug: string): Promise<CMSResponse<Solution>> => {
  return withFallback(
    async () => {
      const solution = await StrapiApiService.getSolutionBySlug(slug);
      return { data: solution };
    },
    () => {
      const jsonResponse = getJsonSolutions({ search: slug });
      const solution = jsonResponse.data?.find(s => s.slug === slug);
      return {
        success: !!solution,
        data: solution,
        error: solution ? undefined : 'Solution not found'
      };
    }
  );
};

// Projects API
export const getProjects = async (filters?: {
  category?: string;
  status?: string;
  year?: string;
  search?: string;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}): Promise<CMSListResponse<Project>> => {
  return withFallback(
    () => StrapiApiService.getProjects({
      category: filters?.category,
      status: filters?.status,
      year: filters?.year,
      search: filters?.search,
      published: filters?.isActive,
      page: filters?.page,
      pageSize: filters?.pageSize
    }),
    () => getJsonProjects(filters)
  );
};

export const getProjectBySlug = async (slug: string): Promise<CMSResponse<Project>> => {
  return withFallback(
    async () => {
      const project = await StrapiApiService.getProjectBySlug(slug);
      return { data: project };
    },
    () => {
      const jsonResponse = getJsonProjects({ search: slug });
      const project = jsonResponse.data?.find(p => p.slug === slug);
      return {
        success: !!project,
        data: project,
        error: project ? undefined : 'Project not found'
      };
    }
  );
};

// Team Members API
export const getTeamMembers = async (filters?: {
  department?: string;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}): Promise<CMSListResponse<TeamMember>> => {
  return withFallback(
    () => StrapiApiService.getTeamMembers({
      department: filters?.department,
      published: filters?.isActive,
      page: filters?.page,
      pageSize: filters?.pageSize
    }),
    () => getJsonTeamMembers(filters)
  );
};

export const getTeamMemberById = async (id: string): Promise<CMSResponse<TeamMember>> => {
  return withFallback(
    async () => {
      const member = await StrapiApiService.findOne('team-members', id, {
        populate: ['image']
      });
      return { data: member.data };
    },
    () => {
      const jsonResponse = getJsonTeamMembers();
      const member = jsonResponse.data?.find(m => m.id === id);
      return {
        success: !!member,
        data: member,
        error: member ? undefined : 'Team member not found'
      };
    }
  );
};

// Testimonials API
export const getTestimonials = async (filters?: {
  category?: string;
  rating?: number;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}): Promise<CMSListResponse<Testimonial>> => {
  return withFallback(
    () => StrapiApiService.getTestimonials({
      category: filters?.category,
      rating: filters?.rating,
      published: filters?.isActive,
      page: filters?.page,
      pageSize: filters?.pageSize
    }),
    () => getJsonTestimonials(filters)
  );
};

// Company Info API
export const getCompanyInfo = async (): Promise<CMSResponse<CompanyInfo>> => {
  return withFallback(
    async () => {
      const companyInfo = await StrapiApiService.getCompanyInfo();
      return { data: companyInfo };
    },
    () => getJsonCompanyInfo()
  );
};

// Site Settings API
export const getSiteSettings = async (): Promise<CMSResponse<SiteSettings>> => {
  return withFallback(
    async () => {
      const settings = await StrapiApiService.getSiteSettings();
      return { data: settings };
    },
    () => getJsonSiteSettings()
  );
};

// Search API
export const searchContent = async (query: string, types?: string[]): Promise<CMSResponse<any[]>> => {
  return withFallback(
    async () => {
      const results = await StrapiApiService.searchContent(query, types);
      return { data: results };
    },
    () => searchJsonContent(query, types)
  );
};

// CRUD Operations for Strapi (Admin only)
export const createService = async (data: Partial<Service>): Promise<CMSResponse<Service>> => {
  if (!USE_STRAPI) {
    return { success: false, error: 'Strapi not enabled' };
  }

  try {
    const response = await StrapiApiService.create('services', data);
    return {
      success: true,
      data: transformStrapiResponse(response.data)
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to create service'
    };
  }
};

export const updateService = async (id: string, data: Partial<Service>): Promise<CMSResponse<Service>> => {
  if (!USE_STRAPI) {
    return { success: false, error: 'Strapi not enabled' };
  }

  try {
    const response = await StrapiApiService.update('services', id, data);
    return {
      success: true,
      data: transformStrapiResponse(response.data)
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to update service'
    };
  }
};

export const deleteService = async (id: string): Promise<CMSResponse<any>> => {
  if (!USE_STRAPI) {
    return { success: false, error: 'Strapi not enabled' };
  }

  try {
    await StrapiApiService.delete('services', id);
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to delete service'
    };
  }
};

// Export configuration status
export const getCMSConfig = () => ({
  useStrapi: USE_STRAPI,
  strapiFallback: STRAPI_FALLBACK,
  strapiUrl: process.env.REACT_APP_STRAPI_URL || 'http://localhost:1337'
});

// Health check
export const checkStrapiHealth = async (): Promise<boolean> => {
  if (!USE_STRAPI) return false;
  
  try {
    await StrapiApiService.find('services', { pagination: { pageSize: 1 } });
    return true;
  } catch (error) {
    console.error('Strapi health check failed:', error);
    return false;
  }
};
