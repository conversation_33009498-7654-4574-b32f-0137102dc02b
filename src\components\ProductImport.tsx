import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  Download, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Globe,
  Package
} from 'lucide-react';

interface ImportResult {
  success: boolean;
  error?: string;
  product?: {
    name: string;
    brand: string;
    model: string;
    description: string;
    specifications: Record<string, string>;
    features: string[];
    images: string[];
  };
}

const ProductImport = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);

  // Supported websites for import
  const supportedSites = [
    {
      name: 'ACS Klima',
      url: 'acsklima.com',
      logo: '/brands/acsklima-logo.png',
      description: 'HVAC and refrigeration equipment'
    },
    {
      name: 'HiRef',
      url: 'hiref.it',
      logo: '/brands/hiref-logo.png',
      description: 'Commercial refrigeration systems'
    },
    {
      name: 'Carrier',
      url: 'carrier.com',
      logo: '/brands/carrier-logo.png',
      description: 'Air conditioning and heating'
    },
    {
      name: 'Daikin',
      url: 'daikin.com',
      logo: '/brands/daikin-logo.png',
      description: 'HVAC solutions'
    }
  ];

  const handleImport = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      // Simulate API call for demo purposes
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful import
      const mockProduct = {
        name: 'Sample HVAC Unit',
        brand: 'Sample Brand',
        model: 'SB-2024',
        description: 'High efficiency HVAC unit with advanced controls and energy-saving features.',
        specifications: {
          'Cooling Capacity': '50 kW',
          'Heating Capacity': '55 kW',
          'Refrigerant': 'R410A',
          'Power Supply': '380V/3Ph/50Hz',
          'Efficiency': 'EER 3.5'
        },
        features: [
          'Energy Efficient',
          'Smart Controls',
          'Low Noise Operation',
          'Easy Maintenance',
          'Compact Design'
        ],
        images: [
          '/products/sample-product-1.jpg',
          '/products/sample-product-2.jpg'
        ]
      };

      setResult({
        success: true,
        product: mockProduct
      });
    } catch (error) {
      setResult({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to import product' 
      });
    } finally {
      setLoading(false);
    }
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Import Form */}
      <Card className="border-border">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Upload className="h-6 w-6 mr-3 text-primary" />
            Import Product from External Site
          </CardTitle>
          <p className="text-muted-foreground">
            Automatically extract product information from supported manufacturer websites
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex space-x-2">
            <div className="flex-1">
              <Input
                placeholder="Enter product URL (e.g., https://www.acsklima.com/products/...)"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="min-h-[44px]"
              />
            </div>
            <Button 
              onClick={handleImport} 
              disabled={loading || !url || !isValidUrl(url)}
              className="min-h-[44px] px-6"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Import
                </>
              )}
            </Button>
          </div>
          
          {/* URL Validation */}
          {url && !isValidUrl(url) && (
            <div className="flex items-center text-sm text-red-600">
              <AlertCircle className="h-4 w-4 mr-2" />
              Please enter a valid URL
            </div>
          )}
        </CardContent>
      </Card>

      {/* Supported Sites */}
      <Card className="border-border">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Globe className="h-5 w-5 mr-2 text-primary" />
            Supported Websites
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            We support automatic product import from these manufacturer websites
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {supportedSites.map((site, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                  <img
                    src={site.logo}
                    alt={`${site.name} Logo`}
                    className="max-w-8 max-h-8 object-contain"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder.svg';
                    }}
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{site.name}</h4>
                  <p className="text-xs text-muted-foreground">{site.url}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Import Result */}
      {result && (
        <Card className={`border-border ${result.success ? 'border-green-200 bg-green-50/50' : 'border-red-200 bg-red-50/50'}`}>
          <CardHeader>
            <CardTitle className="flex items-center">
              {result.success ? (
                <>
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                  Import Successful
                </>
              ) : (
                <>
                  <AlertCircle className="h-5 w-5 mr-2 text-red-600" />
                  Import Failed
                </>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {result.success && result.product ? (
              <div className="space-y-6">
                {/* Product Preview */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-lg mb-3">{result.product.name}</h3>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Brand:</span>
                        <span className="text-sm font-medium">{result.product.brand}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Model:</span>
                        <span className="text-sm font-medium">{result.product.model}</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      {result.product.description}
                    </p>
                    
                    {/* Features */}
                    <div className="mb-4">
                      <h4 className="font-medium text-sm mb-2">Features:</h4>
                      <div className="flex flex-wrap gap-1">
                        {result.product.features.map((feature, idx) => (
                          <Badge key={idx} variant="secondary" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm mb-3">Specifications:</h4>
                    <div className="space-y-2">
                      {Object.entries(result.product.specifications).map(([key, value]) => (
                        <div key={key} className="flex justify-between text-sm">
                          <span className="text-muted-foreground">{key}:</span>
                          <span className="font-medium">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-3 pt-4 border-t border-border">
                  <Button className="flex-1">
                    <Package className="h-4 w-4 mr-2" />
                    Add to Products
                  </Button>
                  <Button variant="outline">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Original
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-red-600">
                <p className="font-medium">Error: {result.error}</p>
                <p className="text-sm mt-1">
                  Please check the URL and try again. Make sure the website is supported and the product page is accessible.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card className="border-border bg-muted/30">
        <CardHeader>
          <CardTitle className="text-lg">How to Import Products</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
            <li>Navigate to a product page on any supported manufacturer website</li>
            <li>Copy the product page URL from your browser</li>
            <li>Paste the URL in the import field above</li>
            <li>Click "Import" to automatically extract product information</li>
            <li>Review the imported data and add it to your product catalog</li>
          </ol>
          
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> The import feature automatically extracts product specifications, 
              features, and images from the source website. You can review and edit the information 
              before adding it to your catalog.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductImport;
