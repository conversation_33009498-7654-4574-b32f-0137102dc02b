import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle } from 'lucide-react';
import { Product } from '@/types/product';

interface ProductCardProps {
  product: Product;
  showCategory?: boolean;
  showTags?: boolean;
  maxFeatures?: number;
  className?: string;
}

const ProductCard = ({ 
  product, 
  showCategory = true, 
  showTags = true, 
  maxFeatures = 3,
  className = ""
}: ProductCardProps) => {
  const mainImage = product.images.find(img => img.isMain) || product.images[0];

  return (
    <Card className={`group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] ${className}`}>
      <div className="aspect-video overflow-hidden rounded-t-lg">
        <img 
          src={mainImage?.url} 
          alt={product.title}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
        />
      </div>
      <CardHeader>
        {(showCategory || showTags) && (
          <div className="flex items-center justify-between mb-2">
            {showCategory && (
              <Badge variant="secondary" className="text-xs">
                {product.category.replace('-', ' ').toUpperCase()}
              </Badge>
            )}
            {showTags && (
              <div className="flex gap-1">
                {product.tags.slice(0, 2).map((tag, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}
        <CardTitle className="text-xl text-primary group-hover:text-primary-dark transition-colors">
          {product.title}
        </CardTitle>
        <CardDescription className="line-clamp-2">
          {product.shortDescription}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {maxFeatures > 0 && (
          <ul className="space-y-2 mb-6">
            {product.features.slice(0, maxFeatures).map((feature, idx) => (
              <li key={idx} className="text-sm text-muted-foreground flex items-start">
                <CheckCircle className="h-3 w-3 text-primary mr-2 mt-0.5 flex-shrink-0" />
                <span className="line-clamp-1">{feature.title}</span>
              </li>
            ))}
          </ul>
        )}
        <div className="flex gap-2">
          <Link to={`/products/${product.slug}`} className="flex-1">
            <Button variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
              Learn More <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Button size="sm" className="bg-gradient-primary hover:opacity-90">
            Quote
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
