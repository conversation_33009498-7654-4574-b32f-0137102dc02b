# Strapi Product Management Setup

## Overview
This guide sets up product management in Strapi CMS with the ability to add products manually and import from external sites like acsklima.com.

## Step 1: Create Strapi Backend

```bash
# Create Strapi project
npx create-strapi-app@latest nile-pro-cms --quickstart

# Navigate to project
cd nile-pro-cms

# Start development server
npm run develop
```

## Step 2: Product Content Type Schema

Create a Product content type in Strapi with the following fields:

```json
{
  "kind": "collectionType",
  "collectionName": "products",
  "info": {
    "singularName": "product",
    "pluralName": "products",
    "displayName": "Product",
    "description": "HVAC and MEP products"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true,
      "maxLength": 255
    },
    "slug": {
      "type": "uid",
      "targetField": "name",
      "required": true
    },
    "description": {
      "type": "richtext",
      "required": true
    },
    "shortDescription": {
      "type": "text",
      "maxLength": 500
    },
    "category": {
      "type": "enumeration",
      "enum": [
        "air-handling-unit",
        "condensing-unit", 
        "heat-recovery-ventilation-unit",
        "energy-recovery-ventilation-unit",
        "fan-coil-unit",
        "ecology-unit",
        "water-source-heat-pump",
        "exhaust-unit"
      ],
      "required": true
    },
    "brand": {
      "type": "string",
      "maxLength": 100
    },
    "model": {
      "type": "string",
      "maxLength": 100
    },
    "price": {
      "type": "decimal",
      "min": 0
    },
    "currency": {
      "type": "string",
      "default": "USD",
      "maxLength": 3
    },
    "specifications": {
      "type": "json"
    },
    "features": {
      "type": "json"
    },
    "technicalData": {
      "type": "json"
    },
    "dimensions": {
      "type": "json"
    },
    "weight": {
      "type": "string",
      "maxLength": 50
    },
    "powerConsumption": {
      "type": "string",
      "maxLength": 100
    },
    "capacity": {
      "type": "string",
      "maxLength": 100
    },
    "efficiency": {
      "type": "string",
      "maxLength": 100
    },
    "images": {
      "type": "media",
      "multiple": true,
      "allowedTypes": ["images"]
    },
    "brochure": {
      "type": "media",
      "multiple": false,
      "allowedTypes": ["files"]
    },
    "manuals": {
      "type": "media",
      "multiple": true,
      "allowedTypes": ["files"]
    },
    "certifications": {
      "type": "json"
    },
    "availability": {
      "type": "enumeration",
      "enum": ["in-stock", "out-of-stock", "pre-order", "discontinued"],
      "default": "in-stock"
    },
    "tags": {
      "type": "json"
    },
    "sourceUrl": {
      "type": "string",
      "maxLength": 500
    },
    "sourceWebsite": {
      "type": "string",
      "maxLength": 100
    },
    "importedAt": {
      "type": "datetime"
    },
    "metaTitle": {
      "type": "string",
      "maxLength": 255
    },
    "metaDescription": {
      "type": "text",
      "maxLength": 500
    },
    "featured": {
      "type": "boolean",
      "default": false
    },
    "popular": {
      "type": "boolean",
      "default": false
    }
  }
}
```

## Step 3: Product Import Plugin

Create a custom plugin for importing products from external sites.

### Install Dependencies

```bash
# In your Strapi project
npm install axios cheerio puppeteer
```

### Create Import Service

Create `src/api/product/services/import.js`:

```javascript
'use strict';

const axios = require('axios');
const cheerio = require('cheerio');

module.exports = ({ strapi }) => ({
  async importFromAcsklima(productUrl) {
    try {
      // Fetch the product page
      const response = await axios.get(productUrl);
      const $ = cheerio.load(response.data);
      
      // Extract product data (customize selectors based on site structure)
      const productData = {
        name: $('h1').first().text().trim(),
        description: $('.product-description').html() || $('.description').html(),
        shortDescription: $('.product-summary').text().trim() || $('.short-description').text().trim(),
        brand: 'ACS Klima',
        sourceUrl: productUrl,
        sourceWebsite: 'acsklima.com',
        importedAt: new Date(),
      };

      // Extract specifications if available
      const specs = {};
      $('.specifications tr, .specs tr').each((i, el) => {
        const key = $(el).find('td:first-child, th:first-child').text().trim();
        const value = $(el).find('td:last-child, th:last-child').text().trim();
        if (key && value) {
          specs[key] = value;
        }
      });
      
      if (Object.keys(specs).length > 0) {
        productData.specifications = specs;
      }

      // Extract features
      const features = [];
      $('.features li, .product-features li').each((i, el) => {
        const feature = $(el).text().trim();
        if (feature) features.push(feature);
      });
      
      if (features.length > 0) {
        productData.features = features;
      }

      // Extract images
      const imageUrls = [];
      $('.product-images img, .gallery img').each((i, el) => {
        const src = $(el).attr('src') || $(el).attr('data-src');
        if (src) {
          imageUrls.push(src.startsWith('http') ? src : `https://www.acsklima.com${src}`);
        }
      });

      // Create product in Strapi
      const product = await strapi.entityService.create('api::product.product', {
        data: {
          ...productData,
          publishedAt: new Date(),
        },
      });

      // Download and attach images (optional)
      if (imageUrls.length > 0) {
        await this.downloadAndAttachImages(product.id, imageUrls);
      }

      return product;
    } catch (error) {
      strapi.log.error('Error importing product:', error);
      throw error;
    }
  },

  async downloadAndAttachImages(productId, imageUrls) {
    // Implementation for downloading and attaching images
    // This would require additional setup for file handling
    console.log('Image URLs to download:', imageUrls);
  },

  async importMultipleProducts(urls) {
    const results = [];
    for (const url of urls) {
      try {
        const product = await this.importFromAcsklima(url);
        results.push({ success: true, product, url });
      } catch (error) {
        results.push({ success: false, error: error.message, url });
      }
    }
    return results;
  }
});
```

### Create Import Controller

Create `src/api/product/controllers/import.js`:

```javascript
'use strict';

module.exports = {
  async importProduct(ctx) {
    try {
      const { url } = ctx.request.body;
      
      if (!url) {
        return ctx.badRequest('Product URL is required');
      }

      const product = await strapi
        .service('api::product.import')
        .importFromAcsklima(url);

      ctx.send({
        success: true,
        data: product,
        message: 'Product imported successfully'
      });
    } catch (error) {
      ctx.badRequest('Failed to import product', { error: error.message });
    }
  },

  async importMultiple(ctx) {
    try {
      const { urls } = ctx.request.body;
      
      if (!urls || !Array.isArray(urls)) {
        return ctx.badRequest('URLs array is required');
      }

      const results = await strapi
        .service('api::product.import')
        .importMultipleProducts(urls);

      ctx.send({
        success: true,
        data: results,
        message: `Processed ${urls.length} products`
      });
    } catch (error) {
      ctx.badRequest('Failed to import products', { error: error.message });
    }
  }
};
```

### Add Import Routes

Create `src/api/product/routes/import.js`:

```javascript
'use strict';

module.exports = {
  routes: [
    {
      method: 'POST',
      path: '/products/import',
      handler: 'import.importProduct',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/products/import-multiple',
      handler: 'import.importMultiple',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
```

## Step 4: Frontend Integration

Create a simple admin interface for importing products:

```typescript
// src/components/ProductImport.tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const ProductImport = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  const handleImport = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/products/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Import Product from External Site</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex space-x-2">
          <Input
            placeholder="Enter product URL (e.g., from acsklima.com)"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
          />
          <Button onClick={handleImport} disabled={loading || !url}>
            {loading ? 'Importing...' : 'Import'}
          </Button>
        </div>
        
        {result && (
          <div className={`p-4 rounded ${result.success ? 'bg-green-100' : 'bg-red-100'}`}>
            {result.success ? (
              <p className="text-green-800">Product imported successfully!</p>
            ) : (
              <p className="text-red-800">Error: {result.error}</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProductImport;
```

## Step 5: Usage Instructions

### Manual Product Entry
1. Go to Strapi admin panel
2. Navigate to Content Manager > Products
3. Click "Create new entry"
4. Fill in product details manually

### Import from External Sites
1. Copy product URL from acsklima.com or similar sites
2. Use the import API endpoint or admin interface
3. The system will automatically extract product data
4. Review and publish the imported product

### API Endpoints
- `POST /api/products/import` - Import single product
- `POST /api/products/import-multiple` - Import multiple products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product

## Step 6: Customization

### Adding New Import Sources
To add support for other websites:
1. Create new import methods in the import service
2. Add website-specific selectors for data extraction
3. Handle different data formats and structures

### Data Validation
Add validation rules in the content type schema to ensure data quality.

### Image Handling
Implement proper image downloading and storage for imported products.

This setup provides a flexible product management system that can handle both manual entry and automated imports from external sources.
