import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Eye,
  Download,
  ExternalLink,
  Building2,
  Award,
  Globe,
  Phone,
  Mail
} from 'lucide-react';

const ProductsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Brand data with logos and information
  const brands = [
    {
      id: 'hiref',
      name: 'HiRef',
      logo: '/brands/hiref-logo.png',
      description: 'Leading manufacturer of commercial refrigeration and HVAC equipment',
      country: 'Italy',
      founded: '1963',
      specialties: ['Commercial Refrigeration', 'HVAC Systems', 'Heat Pumps'],
      website: 'https://www.hiref.it',
      productCount: 5,
      featured: true
    },
    {
      id: 'dkc',
      name: 'DKC',
      logo: '/brands/dkc-logo.png',
      description: 'Premium electrical enclosures and cable management solutions',
      country: 'Russia',
      founded: '1998',
      specialties: ['Electrical Enclosures', 'Cable Management', 'Industrial Solutions'],
      website: 'https://www.dkc.ru',
      productCount: 3,
      featured: true
    },
    {
      id: 'carrier',
      name: 'Carrier',
      logo: '/brands/carrier-logo.png',
      description: 'Global leader in heating, ventilating and air conditioning systems',
      country: 'USA',
      founded: '1915',
      specialties: ['Air Conditioning', 'Heating Systems', 'Refrigeration'],
      website: 'https://www.carrier.com',
      productCount: 4,
      featured: true
    },
    {
      id: 'trane',
      name: 'Trane',
      logo: '/brands/trane-logo.png',
      description: 'Innovative climate control solutions for commercial and residential',
      country: 'USA',
      founded: '1885',
      specialties: ['Climate Control', 'Energy Efficiency', 'Smart Systems'],
      website: 'https://www.trane.com',
      productCount: 2,
      featured: false
    },
    {
      id: 'york',
      name: 'York',
      logo: '/brands/york-logo.png',
      description: 'Comprehensive HVAC solutions for every application',
      country: 'USA',
      founded: '1874',
      specialties: ['HVAC Systems', 'Chillers', 'Air Handlers'],
      website: 'https://www.york.com',
      productCount: 2,
      featured: false
    },
    {
      id: 'daikin',
      name: 'Daikin',
      logo: '/brands/daikin-logo.png',
      description: 'World leader in air conditioning and refrigeration technology',
      country: 'Japan',
      founded: '1924',
      specialties: ['Air Conditioning', 'Heat Pumps', 'Refrigeration'],
      website: 'https://www.daikin.com',
      productCount: 2,
      featured: true
    }
  ];

  // Sample products data organized by brand
  const products = [
    // HiRef Products
    {
      id: 'hiref-001',
      brandId: 'hiref',
      name: 'HiRef TECO+ Air Cooled Chiller',
      model: 'TECO+ 150',
      category: 'air-handling-unit',
      image: '/products/hiref-teco-chiller.jpg',
      price: 25000,
      currency: 'USD',
      description: 'High efficiency air cooled chiller with R410A refrigerant and scroll compressors',
      specifications: {
        'Cooling Capacity': '150 kW',
        'Refrigerant': 'R410A',
        'Compressor Type': 'Scroll',
        'Efficiency': 'EER 3.2',
        'Noise Level': '68 dB(A)'
      },
      features: ['High Efficiency', 'Low Noise', 'Compact Design', 'Easy Maintenance'],
      availability: 'in-stock',
      rating: 4.8,
      reviews: 24,
      featured: true
    },
    {
      id: 'hiref-002',
      brandId: 'hiref',
      name: 'HiRef EWAD Water Cooled Chiller',
      model: 'EWAD 200',
      category: 'condensing-unit',
      image: '/products/hiref-ewad-chiller.jpg',
      price: 32000,
      currency: 'USD',
      description: 'Water cooled chiller with high efficiency and environmental friendly refrigerant',
      specifications: {
        'Cooling Capacity': '200 kW',
        'Refrigerant': 'R134a',
        'Compressor Type': 'Screw',
        'Efficiency': 'EER 5.8',
        'Water Flow': '34.5 m³/h'
      },
      features: ['Water Cooled', 'High COP', 'Microprocessor Control', 'Remote Monitoring'],
      availability: 'in-stock',
      rating: 4.9,
      reviews: 18,
      featured: false
    },
    {
      id: 'hiref-003',
      brandId: 'hiref',
      name: 'HiRef AQUAFORCE Heat Pump',
      model: 'AQUAFORCE 30XW',
      category: 'water-source-heat-pump',
      image: '/products/hiref-aquaforce.jpg',
      price: 28000,
      currency: 'USD',
      description: 'High efficiency water source heat pump with variable speed technology',
      specifications: {
        'Heating Capacity': '180 kW',
        'Cooling Capacity': '160 kW',
        'Refrigerant': 'R410A',
        'COP Heating': '4.2',
        'EER Cooling': '3.8'
      },
      features: ['Variable Speed', 'Heat Recovery', 'Smart Controls', 'Energy Efficient'],
      availability: 'in-stock',
      rating: 4.7,
      reviews: 12,
      featured: true
    },
    // DKC Products
    {
      id: 'dkc-001',
      brandId: 'dkc',
      name: 'DKC ARCA Electrical Enclosure',
      model: 'ARCA 8080',
      category: 'exhaust-unit',
      image: '/products/dkc-arca-enclosure.jpg',
      price: 850,
      currency: 'USD',
      description: 'Premium electrical enclosure with IP65 protection and modular design',
      specifications: {
        'Dimensions': '800x800x300mm',
        'Protection': 'IP65',
        'Material': 'Steel',
        'Thickness': '2mm',
        'Color': 'RAL 7035'
      },
      features: ['IP65 Protection', 'Modular Design', 'Easy Installation', 'Corrosion Resistant'],
      availability: 'in-stock',
      rating: 4.7,
      reviews: 32,
      featured: true
    },
    {
      id: 'dkc-002',
      brandId: 'dkc',
      name: 'DKC Cable Tray System',
      model: 'CT-500',
      category: 'exhaust-unit',
      image: '/products/dkc-cable-tray.jpg',
      price: 120,
      currency: 'USD',
      description: 'Heavy duty cable tray system for industrial applications',
      specifications: {
        'Width': '500mm',
        'Height': '100mm',
        'Length': '3000mm',
        'Load Capacity': '150 kg/m',
        'Material': 'Galvanized Steel'
      },
      features: ['Heavy Duty', 'Galvanized Finish', 'Easy Assembly', 'Flexible Design'],
      availability: 'in-stock',
      rating: 4.6,
      reviews: 28,
      featured: false
    },
    {
      id: 'dkc-003',
      brandId: 'dkc',
      name: 'DKC Terminal Block System',
      model: 'TB-2.5',
      category: 'exhaust-unit',
      image: '/products/dkc-terminal-block.jpg',
      price: 45,
      currency: 'USD',
      description: 'Professional terminal block system for electrical connections',
      specifications: {
        'Cross Section': '2.5 mm²',
        'Voltage': '800V',
        'Current': '24A',
        'Material': 'PA66',
        'Color': 'Gray'
      },
      features: ['High Quality', 'Easy Connection', 'Reliable Contact', 'Compact Size'],
      availability: 'in-stock',
      rating: 4.4,
      reviews: 45,
      featured: false
    },
    {
      id: 'hiref-004',
      brandId: 'hiref',
      name: 'HiRef FOCUS Condensing Unit',
      model: 'FOCUS 80',
      category: 'condensing-unit',
      image: '/products/hiref-focus.jpg',
      price: 8500,
      currency: 'USD',
      description: 'Compact condensing unit for commercial refrigeration applications',
      specifications: {
        'Cooling Capacity': '80 kW',
        'Refrigerant': 'R404A',
        'Compressor Type': 'Semi-hermetic',
        'Evaporating Temp': '-10°C',
        'Condensing Temp': '45°C'
      },
      features: ['Compact Design', 'Commercial Grade', 'Easy Service', 'Reliable Operation'],
      availability: 'in-stock',
      rating: 4.6,
      reviews: 19,
      featured: false
    },
    {
      id: 'hiref-005',
      brandId: 'hiref',
      name: 'HiRef MULTIPOWER Heat Recovery',
      model: 'MP-HRV 5000',
      category: 'heat-recovery-ventilation-unit',
      image: '/products/hiref-multipower.jpg',
      price: 15000,
      currency: 'USD',
      description: 'High efficiency heat recovery ventilation unit with advanced controls',
      specifications: {
        'Airflow': '5000 m³/h',
        'Heat Recovery': '92%',
        'Filter Class': 'F7',
        'Static Pressure': '800 Pa',
        'Power Consumption': '3.2 kW'
      },
      features: ['High Heat Recovery', 'Advanced Controls', 'Energy Efficient', 'Low Maintenance'],
      availability: 'in-stock',
      rating: 4.9,
      reviews: 14,
      featured: true
    },
    // Carrier Products
    {
      id: 'carrier-001',
      brandId: 'carrier',
      name: 'Carrier 30XA Air Cooled Chiller',
      model: '30XA 1002',
      category: 'air-handling-unit',
      image: '/products/carrier-30xa-chiller.jpg',
      price: 45000,
      currency: 'USD',
      description: 'High performance air cooled chiller with variable speed drive technology',
      specifications: {
        'Cooling Capacity': '1000 kW',
        'Refrigerant': 'R134a',
        'Compressor Type': 'Centrifugal',
        'Efficiency': 'IPLV 6.1',
        'Sound Level': '85 dB(A)'
      },
      features: ['Variable Speed Drive', 'High Efficiency', 'Advanced Controls', 'Low Maintenance'],
      availability: 'pre-order',
      rating: 4.9,
      reviews: 15,
      featured: true
    },
    {
      id: 'carrier-002',
      brandId: 'carrier',
      name: 'Carrier AquaEdge Centrifugal Chiller',
      model: '19XR',
      category: 'condensing-unit',
      image: '/products/carrier-aquaedge.jpg',
      price: 65000,
      currency: 'USD',
      description: 'Premium centrifugal chiller with magnetic bearing technology',
      specifications: {
        'Cooling Capacity': '1500 kW',
        'Refrigerant': 'R134a',
        'Compressor Type': 'Magnetic Bearing Centrifugal',
        'Efficiency': 'IPLV 7.2',
        'Operating Range': '10-100%'
      },
      features: ['Magnetic Bearings', 'Oil-Free Operation', 'Variable Speed', 'Smart Controls'],
      availability: 'pre-order',
      rating: 5.0,
      reviews: 8,
      featured: true
    },
    {
      id: 'carrier-003',
      brandId: 'carrier',
      name: 'Carrier 40RU Rooftop Unit',
      model: '40RU 120',
      category: 'air-handling-unit',
      image: '/products/carrier-40ru.jpg',
      price: 28000,
      currency: 'USD',
      description: 'Commercial rooftop unit with gas heat and DX cooling',
      specifications: {
        'Cooling Capacity': '120 kW',
        'Heating Capacity': '150 kW',
        'Airflow': '20000 m³/h',
        'Refrigerant': 'R410A',
        'Gas Input': '180 kW'
      },
      features: ['Gas Heat', 'DX Cooling', 'Economizer', 'Variable Speed Fans'],
      availability: 'in-stock',
      rating: 4.6,
      reviews: 25,
      featured: false
    },
    {
      id: 'carrier-004',
      brandId: 'carrier',
      name: 'Carrier Aero Fan Coil',
      model: 'AERO 42N',
      category: 'fan-coil-unit',
      image: '/products/carrier-aero.jpg',
      price: 1200,
      currency: 'USD',
      description: 'High performance fan coil unit for commercial applications',
      specifications: {
        'Cooling Capacity': '12 kW',
        'Heating Capacity': '15 kW',
        'Airflow': '2000 m³/h',
        'Water Flow': '2.1 l/s',
        'Noise Level': '38 dB(A)'
      },
      features: ['Quiet Operation', 'High Efficiency', 'Compact Design', 'Easy Installation'],
      availability: 'in-stock',
      rating: 4.4,
      reviews: 33,
      featured: false
    },
    // Daikin Products
    {
      id: 'daikin-001',
      brandId: 'daikin',
      name: 'Daikin VRV IV Heat Pump',
      model: 'RXYQ-T',
      category: 'water-source-heat-pump',
      image: '/products/daikin-vrv-iv.jpg',
      price: 18000,
      currency: 'USD',
      description: 'Advanced VRV heat pump system with intelligent control and high efficiency',
      specifications: {
        'Heating Capacity': '140 kW',
        'Cooling Capacity': '125 kW',
        'Refrigerant': 'R410A',
        'COP': '4.5',
        'EER': '4.2'
      },
      features: ['VRV Technology', 'Intelligent Control', 'High Efficiency', 'Quiet Operation'],
      availability: 'in-stock',
      rating: 4.8,
      reviews: 35,
      featured: true
    },
    {
      id: 'daikin-002',
      brandId: 'daikin',
      name: 'Daikin Sky Air Advance',
      model: 'FXA-A',
      category: 'fan-coil-unit',
      image: '/products/daikin-sky-air.jpg',
      price: 3500,
      currency: 'USD',
      description: 'Compact and efficient air conditioning unit for commercial applications',
      specifications: {
        'Cooling Capacity': '35 kW',
        'Heating Capacity': '40 kW',
        'Refrigerant': 'R32',
        'SEER': '16.5',
        'Noise Level': '42 dB(A)'
      },
      features: ['R32 Refrigerant', 'Compact Design', 'Energy Efficient', 'Easy Installation'],
      availability: 'in-stock',
      rating: 4.6,
      reviews: 28,
      featured: false
    },
    // Trane Products
    {
      id: 'trane-001',
      brandId: 'trane',
      name: 'Trane Sintesis Air Handler',
      model: 'TAM 9',
      category: 'air-handling-unit',
      image: '/products/trane-sintesis.jpg',
      price: 22000,
      currency: 'USD',
      description: 'High performance air handling unit with advanced filtration and controls',
      specifications: {
        'Airflow': '50000 m³/h',
        'Static Pressure': '1200 Pa',
        'Filter Class': 'F7/F9',
        'Heat Recovery': '85%',
        'Leakage Class': 'L1'
      },
      features: ['Advanced Filtration', 'Heat Recovery', 'Smart Controls', 'Modular Design'],
      availability: 'in-stock',
      rating: 4.7,
      reviews: 22,
      featured: true
    },
    {
      id: 'trane-002',
      brandId: 'trane',
      name: 'Trane CenTraVac Chiller',
      model: 'CVHE',
      category: 'condensing-unit',
      image: '/products/trane-centravac.jpg',
      price: 85000,
      currency: 'USD',
      description: 'High efficiency centrifugal chiller with magnetic bearings',
      specifications: {
        'Cooling Capacity': '2000 kW',
        'Refrigerant': 'R134a',
        'Compressor Type': 'Centrifugal',
        'IPLV': '8.5',
        'Full Load Efficiency': '6.8'
      },
      features: ['Magnetic Bearings', 'Variable Speed', 'Advanced Controls', 'High Efficiency'],
      availability: 'pre-order',
      rating: 4.9,
      reviews: 12,
      featured: true
    },
    // York Products
    {
      id: 'york-001',
      brandId: 'york',
      name: 'York YMC2 Magnetic Chiller',
      model: 'YMC2 1500',
      category: 'condensing-unit',
      image: '/products/york-ymc2.jpg',
      price: 75000,
      currency: 'USD',
      description: 'Oil-free magnetic bearing centrifugal chiller with exceptional efficiency',
      specifications: {
        'Cooling Capacity': '1500 kW',
        'Refrigerant': 'R1233zd',
        'Compressor Type': 'Magnetic Centrifugal',
        'IPLV': '9.2',
        'Operating Range': '10-100%'
      },
      features: ['Oil-Free Operation', 'Magnetic Bearings', 'Low GWP Refrigerant', 'Smart Controls'],
      availability: 'pre-order',
      rating: 4.8,
      reviews: 16,
      featured: true
    },
    {
      id: 'york-002',
      brandId: 'york',
      name: 'York YLAA Air Cooled Chiller',
      model: 'YLAA 0240',
      category: 'air-handling-unit',
      image: '/products/york-ylaa.jpg',
      price: 35000,
      currency: 'USD',
      description: 'Reliable air cooled chiller with scroll compressors and R410A refrigerant',
      specifications: {
        'Cooling Capacity': '240 kW',
        'Refrigerant': 'R410A',
        'Compressor Type': 'Scroll',
        'EER': '3.4',
        'Sound Level': '72 dB(A)'
      },
      features: ['Scroll Compressors', 'Reliable Operation', 'Easy Maintenance', 'Compact Design'],
      availability: 'in-stock',
      rating: 4.5,
      reviews: 31,
      featured: false
    }
  ];

  // Filter products based on search and filters
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBrand = selectedBrand === 'all' || product.brandId === selectedBrand;
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;

    return matchesSearch && matchesBrand && matchesCategory;
  });

  // Group products by brand
  const productsByBrand = brands.map(brand => ({
    ...brand,
    products: filteredProducts.filter(product => product.brandId === brand.id)
  })).filter(brand => brand.products.length > 0);

  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'air-handling-unit', name: 'Air Handling Units' },
    { id: 'condensing-unit', name: 'Condensing Units' },
    { id: 'heat-recovery-ventilation-unit', name: 'HRV Units' },
    { id: 'energy-recovery-ventilation-unit', name: 'ERV Units' },
    { id: 'fan-coil-unit', name: 'Fan Coil Units' },
    { id: 'ecology-unit', name: 'Ecology Units' },
    { id: 'water-source-heat-pump', name: 'Heat Pumps' },
    { id: 'exhaust-unit', name: 'Exhaust Units' }
  ];

  return (
    <div className="min-h-screen overflow-x-hidden">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Our Products
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Discover premium HVAC and MEP products from world-leading brands.
              Quality equipment for every project requirement.
            </p>
          </div>

          {/* Search and Filters */}
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input
                  placeholder="Search products, models, or descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 min-h-[44px]"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="px-4 py-2 border border-border rounded-md bg-background text-foreground min-h-[44px]"
                >
                  <option value="all">All Brands</option>
                  {brands.map(brand => (
                    <option key={brand.id} value={brand.id}>{brand.name}</option>
                  ))}
                </select>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-border rounded-md bg-background text-foreground min-h-[44px]"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
                <div className="flex border border-border rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Brands Section */}
      <section className="py-16 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Our <span className="text-primary">Partner Brands</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              We partner with the world's leading manufacturers to bring you the highest quality HVAC and MEP equipment.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {brands.filter(brand => brand.featured).map((brand) => (
              <Card key={brand.id} className="border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                <CardContent className="p-8 text-center">
                  <div className="w-24 h-24 mx-auto mb-6 bg-white rounded-lg flex items-center justify-center shadow-sm">
                    <img
                      src={brand.logo}
                      alt={`${brand.name} Logo`}
                      className="max-w-20 max-h-20 object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder.svg';
                      }}
                    />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-2">{brand.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4">{brand.description}</p>
                  <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground mb-4">
                    <span className="flex items-center">
                      <Globe className="h-3 w-3 mr-1" />
                      {brand.country}
                    </span>
                    <span className="flex items-center">
                      <Building2 className="h-3 w-3 mr-1" />
                      {brand.founded}
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-1 justify-center mb-4">
                    {brand.specialties.slice(0, 2).map((specialty, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        setSelectedBrand(brand.id);
                        // Scroll to products section
                        const productsSection = document.getElementById('products-section');
                        if (productsSection) {
                          productsSection.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                    >
                      View Products ({brand.productCount})
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(brand.website, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Products by Brand Section */}
      <section id="products-section" className="py-16 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {productsByBrand.length === 0 ? (
            <div className="text-center py-16">
              <h3 className="text-2xl font-bold text-foreground mb-4">No Products Found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search criteria or browse all products.
              </p>
              <Button onClick={() => {
                setSearchTerm('');
                setSelectedBrand('all');
                setSelectedCategory('all');
              }}>
                Clear Filters
              </Button>
            </div>
          ) : (
            productsByBrand.map((brand) => (
              <div key={brand.id} className="mb-16">
                {/* Brand Header */}
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center shadow-sm">
                      <img
                        src={brand.logo}
                        alt={`${brand.name} Logo`}
                        className="max-w-12 max-h-12 object-contain"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder.svg';
                        }}
                      />
                    </div>
                    <div>
                      <Link to={`/brands/${brand.id}`}>
                        <h2 className="text-2xl md:text-3xl font-bold text-foreground hover:text-primary transition-colors">
                          {brand.name} Products
                        </h2>
                      </Link>
                      <p className="text-muted-foreground">
                        {brand.products.length} product{brand.products.length !== 1 ? 's' : ''} available
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => window.open(brand.website, '_blank')}
                    className="hidden sm:flex"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Visit Website
                  </Button>
                </div>

                {/* Products Grid */}
                <div className={`grid gap-6 ${viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
                  }`}>
                  {brand.products.map((product) => (
                    <Card key={product.id} className="border-border hover:shadow-primary transition-all duration-300 hover:scale-[1.02]">
                      <div className="aspect-video overflow-hidden rounded-t-lg">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder.svg';
                          }}
                        />
                      </div>
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h3 className="text-lg font-bold text-foreground mb-1 line-clamp-2">
                              {product.name}
                            </h3>
                            <p className="text-sm text-muted-foreground mb-2">
                              Model: {product.model}
                            </p>
                          </div>
                          <Badge
                            variant={product.availability === 'in-stock' ? 'default' : 'secondary'}
                            className="ml-2"
                          >
                            {product.availability === 'in-stock' ? 'In Stock' :
                              product.availability === 'pre-order' ? 'Pre-Order' : 'Out of Stock'}
                          </Badge>
                        </div>

                        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                          {product.description}
                        </p>

                        {/* Key Specifications */}
                        <div className="mb-4">
                          <h4 className="text-xs font-semibold text-foreground mb-2">Key Specs:</h4>
                          <div className="space-y-1">
                            {Object.entries(product.specifications).slice(0, 2).map(([key, value]) => (
                              <div key={key} className="flex justify-between text-xs">
                                <span className="text-muted-foreground">{key}:</span>
                                <span className="text-foreground font-medium">{value}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Features */}
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {product.features.slice(0, 3).map((feature, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {feature}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Price and Rating */}
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <span className="text-2xl font-bold text-primary">
                              ${product.price.toLocaleString()}
                            </span>
                            <span className="text-sm text-muted-foreground ml-1">
                              {product.currency}
                            </span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm font-medium">{product.rating}</span>
                            <span className="text-xs text-muted-foreground">
                              ({product.reviews})
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <Button size="sm" className="flex-1">
                            <Eye className="h-3 w-3 mr-1" />
                            View Details
                          </Button>
                          <Button variant="outline" size="sm">
                            <Heart className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ProductsPage;