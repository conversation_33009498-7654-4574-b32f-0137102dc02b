# Strapi Headless CMS Setup Guide for Nile Pro MEP

## Overview
This guide will help you set up Strapi as a headless CMS backend for the Nile Pro MEP website, replacing the current JSON-based CMS with a robust, scalable solution.

## Prerequisites
- Node.js 18+ installed
- npm or yarn package manager
- Database (SQLite for development, PostgreSQL/MySQL for production)

## Step 1: Create Strapi Backend

### Option A: Quick Setup (Recommended)
```bash
# Navigate to your project root directory
cd "e:\CodeSafir\Clients\Nile Pro MEP"

# Create Strapi backend
npx create-strapi-app@latest nile-pro-cms --quickstart

# Navigate to Strapi directory
cd nile-pro-cms

# Start Strapi development server
npm run develop
```

### Option B: Custom Setup
```bash
# Create Strapi with custom database
npx create-strapi-app@latest nile-pro-cms --dbclient=postgres --dbhost=localhost --dbport=5432 --dbname=nile_pro_cms --dbusername=your_username --dbpassword=your_password
```

## Step 2: Configure Strapi

### Environment Configuration
Create `.env` file in `nile-pro-cms` directory:

```env
HOST=0.0.0.0
PORT=1337
APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt
ADMIN_JWT_SECRET=your-admin-jwt-secret
TRANSFER_TOKEN_SALT=your-transfer-token-salt
JWT_SECRET=your-jwt-secret

# Database
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db

# For production with PostgreSQL
# DATABASE_CLIENT=postgres
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=nile_pro_cms
# DATABASE_USERNAME=your_username
# DATABASE_PASSWORD=your_password
# DATABASE_SSL=false
```

### CORS Configuration
Update `config/middlewares.js`:

```javascript
module.exports = [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': ["'self'", 'data:', 'blob:', 'res.cloudinary.com'],
          'media-src': ["'self'", 'data:', 'blob:', 'res.cloudinary.com'],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      headers: '*',
      origin: ['http://localhost:5173', 'http://localhost:3000', 'https://your-domain.com']
    }
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
```

## Step 3: Content Types Structure

### Services Content Type
```json
{
  "kind": "collectionType",
  "collectionName": "services",
  "info": {
    "singularName": "service",
    "pluralName": "services",
    "displayName": "Service"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "slug": {
      "type": "uid",
      "targetField": "title",
      "required": true
    },
    "shortDescription": {
      "type": "text",
      "required": true
    },
    "fullDescription": {
      "type": "richtext",
      "required": true
    },
    "icon": {
      "type": "string",
      "default": "Zap"
    },
    "features": {
      "type": "json"
    },
    "badge": {
      "type": "string"
    },
    "complexity": {
      "type": "enumeration",
      "enum": ["Low", "Medium", "High"],
      "default": "Medium"
    },
    "duration": {
      "type": "string"
    },
    "rating": {
      "type": "decimal",
      "min": 1,
      "max": 5,
      "default": 4.5
    },
    "category": {
      "type": "enumeration",
      "enum": ["electrical", "mechanical", "plumbing", "hvac", "automation", "maintenance"],
      "required": true
    },
    "tags": {
      "type": "json"
    },
    "images": {
      "type": "media",
      "multiple": true,
      "allowedTypes": ["images"]
    }
  }
}
```

### Solutions Content Type
```json
{
  "kind": "collectionType",
  "collectionName": "solutions",
  "info": {
    "singularName": "solution",
    "pluralName": "solutions",
    "displayName": "Solution"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "slug": {
      "type": "uid",
      "targetField": "title",
      "required": true
    },
    "shortDescription": {
      "type": "text",
      "required": true
    },
    "fullDescription": {
      "type": "richtext",
      "required": true
    },
    "detailedDescription": {
      "type": "richtext"
    },
    "icon": {
      "type": "string",
      "default": "Building2"
    },
    "features": {
      "type": "json"
    },
    "projects": {
      "type": "string"
    },
    "category": {
      "type": "enumeration",
      "enum": ["hospitality", "healthcare", "industrial", "office", "food-beverage", "commercial"],
      "required": true
    },
    "services": {
      "type": "json"
    },
    "caseStudies": {
      "type": "json"
    },
    "stats": {
      "type": "json"
    },
    "tags": {
      "type": "json"
    },
    "images": {
      "type": "media",
      "multiple": true,
      "allowedTypes": ["images"]
    }
  }
}
```

### Projects Content Type
```json
{
  "kind": "collectionType",
  "collectionName": "projects",
  "info": {
    "singularName": "project",
    "pluralName": "projects",
    "displayName": "Project"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "title": {
      "type": "string",
      "required": true
    },
    "slug": {
      "type": "uid",
      "targetField": "title",
      "required": true
    },
    "location": {
      "type": "string",
      "required": true
    },
    "category": {
      "type": "enumeration",
      "enum": ["hospitality", "healthcare", "industrial", "commercial", "residential", "infrastructure"],
      "required": true
    },
    "year": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "text",
      "required": true
    },
    "detailedDescription": {
      "type": "richtext"
    },
    "scope": {
      "type": "string"
    },
    "value": {
      "type": "string"
    },
    "duration": {
      "type": "string"
    },
    "client": {
      "type": "string",
      "required": true
    },
    "features": {
      "type": "json"
    },
    "challenges": {
      "type": "json"
    },
    "solutions": {
      "type": "json"
    },
    "results": {
      "type": "json"
    },
    "tags": {
      "type": "json"
    },
    "status": {
      "type": "enumeration",
      "enum": ["completed", "ongoing", "planned"],
      "default": "completed"
    },
    "image": {
      "type": "media",
      "multiple": false,
      "allowedTypes": ["images"]
    },
    "gallery": {
      "type": "media",
      "multiple": true,
      "allowedTypes": ["images"]
    },
    "testimonial": {
      "type": "relation",
      "relation": "oneToOne",
      "target": "api::testimonial.testimonial"
    }
  }
}
```

## Step 4: API Configuration

### Create API Routes
In `config/routes.js`:

```javascript
module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/services',
      handler: 'service.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/services/:id',
      handler: 'service.findOne',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    // Add similar routes for other content types
  ],
};
```

## Step 5: Frontend Integration

### Install Axios for API calls
```bash
npm install axios
```

### Create Strapi API Service
Create `src/services/strapiApi.ts`:

```typescript
import axios from 'axios';

const STRAPI_URL = process.env.REACT_APP_STRAPI_URL || 'http://localhost:1337';

const strapiApi = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add authentication token if available
const token = localStorage.getItem('strapi-token');
if (token) {
  strapiApi.defaults.headers.Authorization = `Bearer ${token}`;
}

export default strapiApi;
```

## Step 6: Data Migration

### Automated Migration Script
A migration script has been created at `scripts/migrate-to-strapi.js` to automatically transfer your existing JSON data to Strapi.

#### Prerequisites
1. Strapi backend running on `http://localhost:1337`
2. API token generated in Strapi admin panel
3. Content types created in Strapi

#### Generate API Token
1. Go to Strapi admin panel: `http://localhost:1337/admin`
2. Navigate to Settings > API Tokens
3. Click "Create new API Token"
4. Name: "Migration Token"
5. Token duration: "Unlimited"
6. Token type: "Full access"
7. Copy the generated token

#### Run Migration
```bash
# Set environment variables
export STRAPI_URL=http://localhost:1337
export STRAPI_API_TOKEN=your-generated-token-here

# Install dependencies (if not already installed)
npm install axios

# Run migration script
node scripts/migrate-to-strapi.js
```

#### Manual Data Entry
Alternatively, you can manually enter data through the Strapi admin panel:
1. Go to `http://localhost:1337/admin`
2. Navigate to Content Manager
3. Create entries for each content type
4. Use the existing JSON data as reference

## Step 7: Deployment

### Production Configuration
- Set up PostgreSQL database
- Configure environment variables
- Set up SSL certificates
- Configure reverse proxy (Nginx)

### Hosting Options
- DigitalOcean App Platform
- Heroku
- AWS EC2
- Vercel (for frontend)

## Benefits of Strapi Integration

1. **Professional Admin Interface**: Rich admin panel with user management
2. **Database Integration**: Proper database with relationships and queries
3. **API Generation**: Automatic REST and GraphQL APIs
4. **Media Management**: Built-in file upload and media library
5. **User Authentication**: Built-in authentication and authorization
6. **Plugins Ecosystem**: Rich ecosystem of plugins
7. **Scalability**: Designed for production use
8. **Content Versioning**: Draft and publish workflow
9. **Multi-language Support**: Internationalization features
10. **Webhooks**: Real-time notifications and integrations

## Next Steps

1. Set up Strapi backend
2. Create content types
3. Configure API permissions
4. Update frontend to use Strapi API
5. Migrate existing data
6. Test and deploy

This setup will provide a professional, scalable headless CMS solution for the Nile Pro MEP website.
